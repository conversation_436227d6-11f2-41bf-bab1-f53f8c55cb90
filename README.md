# 低空三维空域气象预警系统

基于 Web 的低空三维空域气象预警系统前端页面，用于实时展示 0-3000 米低空范围内的气象要素分布与风险预警信息。

## 🎯 系统概述

本系统专为低空飞行器操作员、航空气象管理人员设计，提供直观的三维可视化界面，支持气象数据动态更新、预警信息实时推送、飞行路径规划等功能。

## ✨ 核心功能

### 🌤️ 气象监测
- **实时气象数据展示** - 温度、风速、能见度、湍流等要素
- **三维可视化** - 0-3000米高度范围内的立体气象分布
- **多图层叠加** - 支持多种气象要素同时显示
- **时间序列动画** - 气象数据时间演变动画
- **高度切片** - 任意高度层的气象数据切片显示

### ⚠️ 预警系统
- **实时预警推送** - 自动检测和推送气象风险预警
- **分级预警管理** - 信息、注意、警告、危险四级预警
- **区域预警显示** - 预警区域的三维可视化
- **预警阈值设置** - 可自定义各类气象要素的预警阈值

### 🛩️ 飞行规划
- **交互式路径规划** - 点击地图创建飞行路径
- **路径优化算法** - 自动优化飞行路径
- **风险评估** - 实时评估飞行路径的气象风险
- **路径保存管理** - 保存和管理多条飞行路径

### 📊 数据分析
- **实时数据监控** - 156个气象站点实时数据
- **统计分析** - 气象数据统计和趋势分析
- **测量工具** - 距离、面积、体积测量工具

## 🛠️ 技术架构

- **Vue 3** - 现代响应式前端框架
- **Vite** - 快速构建工具
- **Cesium** - 专业3D地球引擎
- **WebGL** - 硬件加速的3D渲染

## 项目设置

### 安装依赖

```sh
npm install
```

### 开发环境运行

```sh
npm run dev
```

访问 http://localhost:5173 查看地球应用

### 生产环境构建

```sh
npm run build
```

## 📁 项目结构

```
src/
├── components/
│   ├── WeatherWarningSystem.vue    # 主系统界面
│   ├── WeatherVisualization.vue    # 气象数据可视化
│   ├── FlightPlanner.vue          # 飞行路径规划
│   └── CesiumEarth.vue            # Cesium地球引擎
├── services/
│   └── weatherDataService.js      # 气象数据服务
├── App.vue                        # 应用入口
└── main.js                       # 主程序
```

## 🎛️ 系统界面

### 主控制面板
- **显示控制** - 气象图层开关和透明度调节
- **高度控制** - 0-3000米高度范围选择
- **飞行路径** - 路径规划和管理工具
- **预警设置** - 自定义预警阈值

### 三维可视化区域
- **Cesium地球** - 高性能3D地球渲染
- **气象图层** - 温度、风场、降水等要素叠加
- **预警区域** - 风险区域的立体显示
- **飞行路径** - 规划路径的三维展示

### 信息面板
- **实时气象** - 当前位置气象数据
- **活跃预警** - 当前有效的预警信息
- **数据统计** - 系统运行状态统计

## 🌐 数据来源

### 气象站点网络
- **自动气象站** - 156个自动观测站点
- **人工观测站** - 补充人工观测数据
- **更新频率** - 每5分钟更新一次
- **数据要素** - 温度、湿度、气压、风速风向、能见度等

### 数值预报模式
- **高分辨率网格** - 0.05°×0.05°网格分辨率
- **垂直分层** - 0-3000米范围内50米间隔
- **预报时效** - 未来24小时逐小时预报
- **更新周期** - 每6小时更新一次

## 🚀 快速开始

### 环境要求
- Node.js 16.0+
- 现代浏览器（支持WebGL）
- 推荐使用Chrome或Firefox

### 安装和运行

```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 访问系统
# 打开浏览器访问 http://localhost:5173
```

### 系统操作

1. **气象监测模式**
   - 使用左侧控制面板选择要显示的气象要素
   - 调节高度滑块查看不同高度层的数据
   - 点击时间控制器播放气象动画

2. **飞行规划模式**
   - 点击"路径规划"切换到规划模式
   - 在地图上点击创建航点
   - 调整飞行参数和查看风险评估

3. **三维交互**
   - 鼠标左键拖拽：旋转地球
   - 鼠标右键拖拽：平移视图
   - 滚轮：缩放视图
   - 双击：飞行到指定位置

## ⚙️ 配置说明

### 预警阈值配置
```javascript
// 在控制面板中可以调整以下阈值
const warningThresholds = {
  windSpeed: 15,      // 风速预警阈值 (m/s)
  visibility: 1.0,    // 能见度预警阈值 (km)
  turbulence: 'moderate' // 湍流强度预警级别
}
```

### 飞行参数配置
```javascript
// 飞行规划中的默认参数
const flightParams = {
  cruiseSpeed: 25,        // 巡航速度 (m/s)
  maxClimbRate: 3.0,      // 最大爬升率 (m/s)
  maxDescentRate: 2.5,    // 最大下降率 (m/s)
  turnRadius: 100,        // 转弯半径 (m)
  safetyMargin: 50        // 安全间隔 (m)
}
```

## 🔧 开发指南

### 添加新的气象要素
1. 在 `weatherDataService.js` 中添加数据生成逻辑
2. 在 `WeatherVisualization.vue` 中添加图层配置
3. 更新颜色图例和显示逻辑

### 自定义预警规则
1. 修改 `weatherDataService.js` 中的预警生成逻辑
2. 在 `WeatherWarningSystem.vue` 中更新预警显示
3. 添加新的预警类型和图标

## 📊 性能优化

- **数据分层加载** - 根据视角高度动态加载数据
- **LOD渲染** - 距离相关的细节层次渲染
- **数据缓存** - 智能缓存减少网络请求
- **WebWorker** - 后台处理复杂计算

## 🔗 相关文档

- [Cesium官方文档](https://cesium.com/learn/)
- [Vue 3文档](https://vuejs.org/)
- [Vite配置指南](https://vitejs.dev/config/)
- [WebGL规范](https://www.khronos.org/webgl/)
