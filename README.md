# Cesium 地球应用

这是一个基于 Vue 3 + Vite + Cesium 的3D地球应用。

## 功能特性

- 🌍 多种地球实现方式
- 🎮 交互式地球导航
- 🚀 基于现代前端技术栈
- ⚡ 快速开发和热重载
- 🎨 简单CSS 3D地球
- 🌐 专业Cesium地球
- 📱 响应式设计

## 技术栈

- **Vue 3** - 渐进式JavaScript框架
- **Vite** - 下一代前端构建工具
- **Cesium** - 开源3D地球和地图库

## 项目设置

### 安装依赖

```sh
npm install
```

### 开发环境运行

```sh
npm run dev
```

访问 http://localhost:5173 查看地球应用

### 生产环境构建

```sh
npm run build
```

## 项目结构

```
src/
├── components/
│   ├── EarthSelector.vue   # 地球选择器组件
│   ├── SimpleEarth.vue     # CSS 3D简单地球
│   ├── CesiumCDN.vue       # Cesium CDN版本
│   ├── CesiumEarth.vue     # Cesium本地版本
│   └── BasicCesium.vue     # 基础Cesium组件
├── App.vue                 # 主应用组件
└── main.js                # 应用入口
```

## 地球实现方式

### 1. 简单地球 (SimpleEarth.vue)
- ✅ 基于CSS 3D变换
- ✅ 无需外部依赖
- ✅ 轻量级实现
- ✅ 鼠标交互控制
- ✅ 动画效果

### 2. Cesium CDN (CesiumCDN.vue)
- 🌍 专业级3D地球
- 🗺️ 真实地理数据
- 📡 需要网络连接
- 🚀 功能丰富

### 3. Cesium 本地 (CesiumEarth.vue)
- ⚡ 本地部署
- 🛠️ 高度可定制
- 🔧 配置复杂
- 💪 性能优秀

## 使用说明

1. 启动开发服务器后，你将看到地球选择器
2. 选择你想要体验的地球实现方式：
   - **简单地球**：轻量级CSS 3D实现
   - **Cesium CDN**：专业级地球（需网络）
   - **Cesium 本地**：本地部署版本
3. 使用鼠标进行交互：
   - 左键拖拽：旋转地球
   - 右键拖拽：平移视图（Cesium版本）
   - 滚轮：缩放
4. 使用控制按钮进行快速导航

## 自定义配置

参考 [Vite 配置文档](https://vite.dev/config/) 和 [Cesium 文档](https://cesium.com/learn/)
