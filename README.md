# Cesium 地球应用

这是一个基于 Vue 3 + Vite + Cesium 的3D地球应用。

## 功能特性

- 🌍 3D地球展示
- 🎮 交互式地球导航
- 🚀 基于现代前端技术栈
- ⚡ 快速开发和热重载

## 技术栈

- **Vue 3** - 渐进式JavaScript框架
- **Vite** - 下一代前端构建工具
- **Cesium** - 开源3D地球和地图库

## 项目设置

### 安装依赖

```sh
npm install
```

### 开发环境运行

```sh
npm run dev
```

访问 http://localhost:5173 查看地球应用

### 生产环境构建

```sh
npm run build
```

## 项目结构

```
src/
├── components/
│   ├── CesiumEarth.vue     # 完整功能的Cesium地球组件
│   └── SimpleCesium.vue    # 简化版Cesium地球组件
├── App.vue                 # 主应用组件
└── main.js                # 应用入口

```

## 使用说明

1. 启动开发服务器后，你将看到一个3D地球
2. 使用鼠标进行交互：
   - 左键拖拽：旋转地球
   - 右键拖拽：平移视图
   - 滚轮：缩放
3. 点击主页按钮可以重置视角

## 自定义配置

参考 [Vite 配置文档](https://vite.dev/config/) 和 [Cesium 文档](https://cesium.com/learn/)
