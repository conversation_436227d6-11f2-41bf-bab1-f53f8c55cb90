<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>低空三维空域气象预警系统 - 演示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            overflow-x: hidden;
        }

        .hero {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            position: relative;
        }

        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }

        .hero-content {
            position: relative;
            z-index: 1;
            max-width: 1200px;
            padding: 0 20px;
        }

        .hero h1 {
            font-size: 3.5rem;
            margin-bottom: 20px;
            background: linear-gradient(45deg, #fff, #87ceeb);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .hero p {
            font-size: 1.3rem;
            margin-bottom: 40px;
            opacity: 0.9;
            line-height: 1.6;
        }

        .cta-button {
            display: inline-block;
            padding: 15px 40px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            color: white;
            text-decoration: none;
            border-radius: 50px;
            font-size: 1.2rem;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        .cta-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.4);
        }

        .features {
            padding: 100px 20px;
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
        }

        .features-container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .features h2 {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 60px;
            color: #87ceeb;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 40px;
        }

        .feature-card {
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .feature-card:hover {
            transform: translateY(-10px);
            background: rgba(255, 255, 255, 0.15);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        .feature-icon {
            font-size: 3rem;
            margin-bottom: 20px;
            display: block;
        }

        .feature-card h3 {
            font-size: 1.5rem;
            margin-bottom: 15px;
            color: #87ceeb;
        }

        .feature-card p {
            opacity: 0.9;
            line-height: 1.6;
        }

        .tech-stack {
            padding: 100px 20px;
            background: rgba(0, 0, 0, 0.2);
        }

        .tech-container {
            max-width: 1200px;
            margin: 0 auto;
            text-align: center;
        }

        .tech-stack h2 {
            font-size: 2.5rem;
            margin-bottom: 60px;
            color: #87ceeb;
        }

        .tech-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 30px;
        }

        .tech-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            transition: all 0.3s ease;
        }

        .tech-item:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: scale(1.05);
        }

        .tech-item h4 {
            font-size: 1.2rem;
            margin-bottom: 10px;
            color: #4ecdc4;
        }

        .demo-section {
            padding: 100px 20px;
            text-align: center;
        }

        .demo-container {
            max-width: 800px;
            margin: 0 auto;
        }

        .demo-section h2 {
            font-size: 2.5rem;
            margin-bottom: 30px;
            color: #87ceeb;
        }

        .demo-section p {
            font-size: 1.2rem;
            margin-bottom: 40px;
            opacity: 0.9;
        }

        .demo-features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }

        .demo-feature {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #4ecdc4;
        }

        .demo-feature h4 {
            color: #4ecdc4;
            margin-bottom: 10px;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            background: #27ae60;
            border-radius: 50%;
            margin-right: 8px;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .footer {
            padding: 40px 20px;
            background: rgba(0, 0, 0, 0.3);
            text-align: center;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        .footer p {
            opacity: 0.7;
        }

        @media (max-width: 768px) {
            .hero h1 {
                font-size: 2.5rem;
            }
            
            .hero p {
                font-size: 1.1rem;
            }
            
            .features-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <section class="hero">
        <div class="hero-content">
            <h1>🌩️ 低空三维空域气象预警系统</h1>
            <p>
                专业的Web端低空气象监测与预警平台<br>
                为低空飞行器操作员和航空气象管理人员提供实时、准确的气象信息服务
            </p>
            <a href="http://localhost:5173/" class="cta-button" target="_blank">
                🚀 启动系统
            </a>
        </div>
    </section>

    <section class="features">
        <div class="features-container">
            <h2>🎯 核心功能</h2>
            <div class="features-grid">
                <div class="feature-card">
                    <span class="feature-icon">🌤️</span>
                    <h3>实时气象监测</h3>
                    <p>0-3000米高度范围内的温度、风速、能见度、湍流等气象要素实时监测，支持多图层叠加显示和时间序列动画。</p>
                </div>
                
                <div class="feature-card">
                    <span class="feature-icon">⚠️</span>
                    <h3>智能预警系统</h3>
                    <p>自动检测气象风险，提供分级预警管理，支持自定义预警阈值，实时推送预警信息到相关人员。</p>
                </div>
                
                <div class="feature-card">
                    <span class="feature-icon">🛩️</span>
                    <h3>飞行路径规划</h3>
                    <p>交互式飞行路径规划工具，支持路径优化算法，实时评估飞行风险，提供安全飞行建议。</p>
                </div>
                
                <div class="feature-card">
                    <span class="feature-icon">🌍</span>
                    <h3>三维可视化</h3>
                    <p>基于Cesium的专业3D地球引擎，提供高质量的三维气象数据可视化，支持多角度观察和交互操作。</p>
                </div>
                
                <div class="feature-card">
                    <span class="feature-icon">📊</span>
                    <h3>数据分析</h3>
                    <p>156个气象站点实时数据统计分析，提供距离、面积、体积测量工具，支持数据导出和报告生成。</p>
                </div>
                
                <div class="feature-card">
                    <span class="feature-icon">⚡</span>
                    <h3>高性能渲染</h3>
                    <p>采用WebGL硬件加速，支持大数据量实时渲染，智能LOD和数据缓存，确保流畅的用户体验。</p>
                </div>
            </div>
        </div>
    </section>

    <section class="tech-stack">
        <div class="tech-container">
            <h2>🛠️ 技术架构</h2>
            <div class="tech-grid">
                <div class="tech-item">
                    <h4>Vue 3</h4>
                    <p>现代响应式前端框架</p>
                </div>
                <div class="tech-item">
                    <h4>Cesium</h4>
                    <p>专业3D地球引擎</p>
                </div>
                <div class="tech-item">
                    <h4>WebGL</h4>
                    <p>硬件加速3D渲染</p>
                </div>
                <div class="tech-item">
                    <h4>Vite</h4>
                    <p>快速构建工具</p>
                </div>
            </div>
        </div>
    </section>

    <section class="demo-section">
        <div class="demo-container">
            <h2>🎮 系统演示</h2>
            <p>
                <span class="status-indicator"></span>
                系统当前运行状态：在线
            </p>
            
            <div class="demo-features">
                <div class="demo-feature">
                    <h4>🌡️ 实时数据</h4>
                    <p>156个气象站点<br>5分钟更新频率</p>
                </div>
                <div class="demo-feature">
                    <h4>⚠️ 活跃预警</h4>
                    <p>3个预警区域<br>实时风险监控</p>
                </div>
                <div class="demo-feature">
                    <h4>🛩️ 飞行规划</h4>
                    <p>智能路径优化<br>风险评估分析</p>
                </div>
            </div>
            
            <a href="http://localhost:5173/" class="cta-button" target="_blank">
                🌐 进入系统
            </a>
        </div>
    </section>

    <footer class="footer">
        <p>&copy; 2024 低空三维空域气象预警系统 | 基于Vue 3 + Cesium构建</p>
    </footer>

    <script>
        // 添加一些动态效果
        document.addEventListener('DOMContentLoaded', function() {
            // 平滑滚动
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    document.querySelector(this.getAttribute('href')).scrollIntoView({
                        behavior: 'smooth'
                    });
                });
            });

            // 滚动动画
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver(function(entries) {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }
                });
            }, observerOptions);

            // 观察所有卡片元素
            document.querySelectorAll('.feature-card, .tech-item, .demo-feature').forEach(card => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';
                card.style.transition = 'all 0.6s ease';
                observer.observe(card);
            });
        });
    </script>
</body>
</html>
