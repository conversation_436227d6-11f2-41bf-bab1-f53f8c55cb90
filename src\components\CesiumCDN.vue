<template>
  <div class="cesium-wrapper">
    <div class="header">
      <h1>🌍 Cesium 地球</h1>
      <p>基于 Vue 3 + Cesium CDN 的3D地球应用</p>
    </div>
    
    <div class="controls" v-if="cesiumLoaded">
      <button @click="flyToChina" class="btn">🇨🇳 中国</button>
      <button @click="flyToUSA" class="btn">🇺🇸 美国</button>
      <button @click="flyToEurope" class="btn">🇪🇺 欧洲</button>
      <button @click="resetView" class="btn">🏠 重置</button>
    </div>
    
    <div id="cesiumContainer" class="cesium-container"></div>
    
    <div class="status" v-if="status">
      {{ status }}
    </div>
  </div>
</template>

<script setup>
import { onMounted, onUnmounted, ref } from 'vue'

let viewer = null
const status = ref('正在初始化Cesium...')
const cesiumLoaded = ref(false)

onMounted(async () => {
  try {
    status.value = '正在加载Cesium库...'
    
    // 加载Cesium CSS
    const cssLink = document.createElement('link')
    cssLink.rel = 'stylesheet'
    cssLink.href = 'https://cesium.com/downloads/cesiumjs/releases/1.110/Build/Cesium/Widgets/widgets.css'
    document.head.appendChild(cssLink)
    
    // 加载Cesium JS
    const script = document.createElement('script')
    script.src = 'https://cesium.com/downloads/cesiumjs/releases/1.110/Build/Cesium/Cesium.js'
    script.onload = () => {
      initializeCesium()
    }
    script.onerror = () => {
      status.value = '无法加载Cesium库，请检查网络连接'
    }
    document.head.appendChild(script)
    
  } catch (error) {
    console.error('Cesium加载失败:', error)
    status.value = '地球加载失败，请刷新页面重试'
  }
})

const initializeCesium = () => {
  try {
    status.value = '正在创建地球...'
    
    // 设置Cesium Ion访问令牌（可选）
    if (window.Cesium && window.Cesium.Ion) {
      // 使用默认的公共访问令牌或者不设置
      // window.Cesium.Ion.defaultAccessToken = 'your_token_here'
    }
    
    // 创建Cesium Viewer
    viewer = new window.Cesium.Viewer('cesiumContainer', {
      terrainProvider: new window.Cesium.EllipsoidTerrainProvider(),
      baseLayerPicker: false,
      geocoder: false,
      homeButton: false,
      sceneModePicker: false,
      navigationHelpButton: false,
      animation: false,
      timeline: false,
      fullscreenButton: false,
      vrButton: false,
      scene3DOnly: true,
      requestRenderMode: true,
      maximumRenderTimeChange: Infinity
    })

    // 设置初始视角
    viewer.camera.setView({
      destination: window.Cesium.Cartesian3.fromDegrees(0, 0, 20000000)
    })

    // 禁用默认的双击行为
    viewer.cesiumWidget.screenSpaceEventHandler.removeInputAction(
      window.Cesium.ScreenSpaceEventType.LEFT_DOUBLE_CLICK
    )

    cesiumLoaded.value = true
    status.value = '地球加载完成！'
    
    // 3秒后隐藏状态信息
    setTimeout(() => {
      status.value = ''
    }, 3000)

    console.log('Cesium地球初始化成功')
    
  } catch (error) {
    console.error('Cesium初始化失败:', error)
    status.value = '地球初始化失败：' + error.message
  }
}

onUnmounted(() => {
  if (viewer) {
    viewer.destroy()
    viewer = null
  }
})

// 控制函数
const flyToChina = () => {
  if (viewer && window.Cesium) {
    viewer.camera.flyTo({
      destination: window.Cesium.Cartesian3.fromDegrees(104.0, 35.0, 15000000),
      duration: 2.0
    })
  }
}

const flyToUSA = () => {
  if (viewer && window.Cesium) {
    viewer.camera.flyTo({
      destination: window.Cesium.Cartesian3.fromDegrees(-98.0, 39.0, 15000000),
      duration: 2.0
    })
  }
}

const flyToEurope = () => {
  if (viewer && window.Cesium) {
    viewer.camera.flyTo({
      destination: window.Cesium.Cartesian3.fromDegrees(10.0, 50.0, 15000000),
      duration: 2.0
    })
  }
}

const resetView = () => {
  if (viewer && window.Cesium) {
    viewer.camera.flyTo({
      destination: window.Cesium.Cartesian3.fromDegrees(0, 0, 20000000),
      duration: 2.0
    })
  }
}
</script>

<style scoped>
.cesium-wrapper {
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: hidden;
  background: #000;
}

.header {
  position: absolute;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1000;
  text-align: center;
  color: white;
  background: rgba(0, 0, 0, 0.7);
  padding: 15px 30px;
  border-radius: 10px;
  backdrop-filter: blur(10px);
}

.header h1 {
  margin: 0 0 5px 0;
  font-size: 24px;
}

.header p {
  margin: 0;
  font-size: 14px;
  opacity: 0.8;
}

.controls {
  position: absolute;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1000;
  display: flex;
  gap: 15px;
  background: rgba(0, 0, 0, 0.7);
  padding: 15px;
  border-radius: 10px;
  backdrop-filter: blur(10px);
}

.btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 12px 20px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

.btn:active {
  transform: translateY(0);
}

.cesium-container {
  width: 100%;
  height: 100vh;
}

.status {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1001;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 20px 40px;
  border-radius: 10px;
  font-size: 16px;
  text-align: center;
  backdrop-filter: blur(10px);
}

/* 隐藏Cesium的版权信息 */
:deep(.cesium-widget-credits) {
  display: none !important;
}

/* 隐藏Cesium的logo */
:deep(.cesium-widget-logo) {
  display: none !important;
}
</style>
