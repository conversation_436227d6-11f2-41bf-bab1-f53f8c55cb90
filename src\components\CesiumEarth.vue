<template>
  <div class="cesium-wrapper">
    <div class="controls">
      <h2>🌍 Cesium 地球</h2>
      <div class="control-buttons">
        <button @click="flyToChina" class="btn">飞到中国</button>
        <button @click="flyToUSA" class="btn">飞到美国</button>
        <button @click="resetView" class="btn">重置视角</button>
      </div>
    </div>
    <div id="cesiumContainer" class="cesium-container"></div>
  </div>
</template>

<script setup>
import { onMounted, onUnmounted } from 'vue'

let viewer = null
let Cesium = null

onMounted(async () => {
  try {
    // 动态导入Cesium
    Cesium = await import('cesium')

    // 创建Cesium Viewer
    viewer = new Cesium.Viewer('cesiumContainer', {
      // 基础配置
      animation: false,          // 不显示动画控件
      baseLayerPicker: false,    // 不显示图层选择器
      fullscreenButton: false,   // 不显示全屏按钮
      geocoder: false,           // 不显示地理编码器
      homeButton: true,          // 显示主页按钮
      infoBox: false,            // 不显示信息框
      sceneModePicker: false,    // 不显示场景模式选择器
      selectionIndicator: false, // 不显示选择指示器
      timeline: false,           // 不显示时间轴
      navigationHelpButton: false, // 不显示导航帮助按钮

      // 默认视图
      scene3DOnly: true,
    })

  // 设置初始视角（中国上空）
  viewer.camera.setView({
    destination: Cesium.Cartesian3.fromDegrees(104.0, 35.0, 15000000.0),
    orientation: {
      heading: 0.0,
      pitch: -Cesium.Math.PI_OVER_TWO,
      roll: 0.0
    }
  })

  // 禁用默认的双击行为
  viewer.cesiumWidget.screenSpaceEventHandler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_DOUBLE_CLICK)

    // 设置地球的一些视觉效果
    viewer.scene.globe.enableLighting = true
    viewer.scene.globe.atmosphereHueShift = 0.0
    viewer.scene.globe.atmosphereSaturationShift = 0.0
    viewer.scene.globe.atmosphereBrightnessShift = 0.0

    console.log('Cesium地球初始化完成')
  } catch (error) {
    console.error('Cesium初始化失败:', error)
  }
})

onUnmounted(() => {
  if (viewer) {
    viewer.destroy()
    viewer = null
  }
})

// 控制函数
const flyToChina = () => {
  if (viewer && Cesium) {
    viewer.camera.flyTo({
      destination: Cesium.Cartesian3.fromDegrees(104.0, 35.0, 15000000.0),
      duration: 2.0
    })
  }
}

const flyToUSA = () => {
  if (viewer && Cesium) {
    viewer.camera.flyTo({
      destination: Cesium.Cartesian3.fromDegrees(-98.0, 39.0, 15000000.0),
      duration: 2.0
    })
  }
}

const resetView = () => {
  if (viewer) {
    viewer.camera.setView({
      destination: viewer.camera.positionWC
    })
  }
}

// 暴露viewer实例供父组件使用
defineExpose({
  getViewer: () => viewer,
  flyToChina,
  flyToUSA,
  resetView
})
</script>

<style scoped>
.cesium-wrapper {
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: hidden;
}

.controls {
  position: absolute;
  top: 20px;
  left: 20px;
  z-index: 1000;
  background: rgba(42, 42, 42, 0.8);
  padding: 15px;
  border-radius: 8px;
  backdrop-filter: blur(10px);
}

.controls h2 {
  color: white;
  margin: 0 0 15px 0;
  font-size: 18px;
  font-weight: 600;
}

.control-buttons {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.btn {
  background: #48b884;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
}

.btn:hover {
  background: #369870;
  transform: translateY(-1px);
}

.btn:active {
  transform: translateY(0);
}

.cesium-container {
  width: 100%;
  height: 100vh;
  margin: 0;
  padding: 0;
  overflow: hidden;
  font-family: sans-serif;
}

/* 隐藏Cesium的版权信息 */
:deep(.cesium-widget-credits) {
  display: none !important;
}

/* 自定义导航控件样式 */
:deep(.cesium-navigation-help) {
  display: none;
}

:deep(.cesium-viewer-toolbar) {
  position: absolute;
  top: 20px;
  right: 20px;
}
</style>
