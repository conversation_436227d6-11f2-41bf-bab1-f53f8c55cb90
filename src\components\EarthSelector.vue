<template>
  <div class="selector-wrapper">
    <div class="header">
      <h1>🌍 地球应用选择器</h1>
      <p>选择你想要体验的地球实现方式</p>
    </div>
    
    <div class="options">
      <div class="option-card" @click="selectEarth('simple')">
        <div class="card-icon">🎨</div>
        <h3>简单地球</h3>
        <p>基于CSS 3D的简单地球演示</p>
        <ul>
          <li>✅ 无需网络连接</li>
          <li>✅ 轻量级实现</li>
          <li>✅ 鼠标交互</li>
          <li>✅ 动画效果</li>
        </ul>
        <button class="select-btn">选择此版本</button>
      </div>
      
      <div class="option-card" @click="selectEarth('cesium-cdn')">
        <div class="card-icon">🌐</div>
        <h3>Cesium CDN</h3>
        <p>基于Cesium CDN的专业地球</p>
        <ul>
          <li>🌍 真实地球数据</li>
          <li>🗺️ 高质量渲染</li>
          <li>🚀 专业级功能</li>
          <li>📡 需要网络连接</li>
        </ul>
        <button class="select-btn">选择此版本</button>
      </div>
      
      <div class="option-card" @click="selectEarth('cesium-local')">
        <div class="card-icon">⚡</div>
        <h3>Cesium 本地</h3>
        <p>基于本地Cesium库的地球</p>
        <ul>
          <li>🔧 本地部署</li>
          <li>⚡ 快速加载</li>
          <li>🛠️ 可定制性强</li>
          <li>⚠️ 配置复杂</li>
        </ul>
        <button class="select-btn">选择此版本</button>
      </div>
    </div>
    
    <div class="current-selection" v-if="selectedEarth">
      <h3>当前选择：{{ getEarthName(selectedEarth) }}</h3>
      <button @click="goBack" class="back-btn">返回选择</button>
    </div>
    
    <!-- 动态组件渲染 -->
    <component :is="currentComponent" v-if="selectedEarth" />
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import SimpleEarth from './SimpleEarth.vue'
import CesiumCDN from './CesiumCDN.vue'
import CesiumEarth from './CesiumEarth.vue'

const selectedEarth = ref('')

const currentComponent = computed(() => {
  switch (selectedEarth.value) {
    case 'simple':
      return SimpleEarth
    case 'cesium-cdn':
      return CesiumCDN
    case 'cesium-local':
      return CesiumEarth
    default:
      return null
  }
})

const selectEarth = (type) => {
  selectedEarth.value = type
}

const goBack = () => {
  selectedEarth.value = ''
}

const getEarthName = (type) => {
  const names = {
    'simple': '简单地球',
    'cesium-cdn': 'Cesium CDN',
    'cesium-local': 'Cesium 本地'
  }
  return names[type] || type
}
</script>

<style scoped>
.selector-wrapper {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.header {
  text-align: center;
  color: white;
  margin-bottom: 40px;
}

.header h1 {
  font-size: 2.5rem;
  margin-bottom: 10px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.header p {
  font-size: 1.2rem;
  opacity: 0.9;
}

.options {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.option-card {
  background: white;
  border-radius: 15px;
  padding: 30px;
  text-align: center;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.option-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.option-card:hover::before {
  left: 100%;
}

.option-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.card-icon {
  font-size: 3rem;
  margin-bottom: 20px;
}

.option-card h3 {
  color: #333;
  margin-bottom: 15px;
  font-size: 1.5rem;
}

.option-card p {
  color: #666;
  margin-bottom: 20px;
  font-size: 1rem;
}

.option-card ul {
  list-style: none;
  padding: 0;
  margin: 20px 0;
  text-align: left;
}

.option-card li {
  padding: 5px 0;
  color: #555;
  font-size: 0.9rem;
}

.select-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 12px 30px;
  border-radius: 25px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 600;
  transition: all 0.3s ease;
  margin-top: 20px;
}

.select-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.current-selection {
  position: fixed;
  top: 20px;
  right: 20px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 15px 25px;
  border-radius: 10px;
  z-index: 1000;
  backdrop-filter: blur(10px);
}

.current-selection h3 {
  margin: 0 0 10px 0;
  font-size: 1rem;
}

.back-btn {
  background: #ff6b6b;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 5px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.back-btn:hover {
  background: #ff5252;
}

/* 当选择了地球时隐藏选择器 */
.selector-wrapper:has(.current-selection) .header,
.selector-wrapper:has(.current-selection) .options {
  display: none;
}
</style>
