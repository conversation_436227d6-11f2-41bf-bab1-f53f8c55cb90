<template>
  <div class="flight-planner">
    <div class="planner-header">
      <h3>🛩️ 飞行路径规划</h3>
      <div class="planner-actions">
        <button @click="newRoute" class="btn btn-primary">新建路径</button>
        <button @click="saveRoute" class="btn btn-success" :disabled="!hasActiveRoute">保存路径</button>
        <button @click="clearRoute" class="btn btn-danger" :disabled="!hasActiveRoute">清除路径</button>
      </div>
    </div>

    <div class="planner-content">
      <!-- 路径信息 -->
      <div class="route-info" v-if="activeRoute">
        <h4>📋 路径信息</h4>
        <div class="info-grid">
          <div class="info-item">
            <span class="label">路径名称:</span>
            <input v-model="activeRoute.name" class="route-name-input" />
          </div>
          <div class="info-item">
            <span class="label">航点数量:</span>
            <span class="value">{{ activeRoute.waypoints.length }}</span>
          </div>
          <div class="info-item">
            <span class="label">总距离:</span>
            <span class="value">{{ totalDistance.toFixed(2) }} km</span>
          </div>
          <div class="info-item">
            <span class="label">预计时间:</span>
            <span class="value">{{ estimatedTime }} 分钟</span>
          </div>
          <div class="info-item">
            <span class="label">平均高度:</span>
            <span class="value">{{ averageAltitude.toFixed(0) }} m</span>
          </div>
          <div class="info-item">
            <span class="label">风险等级:</span>
            <span class="value" :class="riskLevelClass">{{ riskLevel }}</span>
          </div>
        </div>
      </div>

      <!-- 航点列表 -->
      <div class="waypoints-section" v-if="activeRoute">
        <h4>📍 航点列表</h4>
        <div class="waypoints-controls">
          <button @click="addWaypoint" class="btn btn-sm">➕ 添加航点</button>
          <button @click="optimizeRoute" class="btn btn-sm">🎯 优化路径</button>
          <button @click="reverseRoute" class="btn btn-sm">🔄 反向路径</button>
        </div>
        
        <div class="waypoints-list">
          <div 
            v-for="(waypoint, index) in activeRoute.waypoints" 
            :key="waypoint.id"
            class="waypoint-item"
            :class="{ selected: selectedWaypoint === index }"
            @click="selectWaypoint(index)"
          >
            <div class="waypoint-header">
              <span class="waypoint-number">{{ index + 1 }}</span>
              <input v-model="waypoint.name" class="waypoint-name" placeholder="航点名称" />
              <button @click="removeWaypoint(index)" class="remove-btn">×</button>
            </div>
            
            <div class="waypoint-details">
              <div class="coordinate-group">
                <label>经度:</label>
                <input type="number" v-model="waypoint.longitude" step="0.000001" />
              </div>
              <div class="coordinate-group">
                <label>纬度:</label>
                <input type="number" v-model="waypoint.latitude" step="0.000001" />
              </div>
              <div class="coordinate-group">
                <label>高度:</label>
                <input type="number" v-model="waypoint.altitude" min="0" max="3000" step="10" />
                <span class="unit">m</span>
              </div>
              <div class="coordinate-group">
                <label>速度:</label>
                <input type="number" v-model="waypoint.speed" min="1" max="100" step="1" />
                <span class="unit">m/s</span>
              </div>
            </div>

            <div class="waypoint-weather" v-if="waypoint.weather">
              <div class="weather-summary">
                <span class="weather-item">🌡️ {{ waypoint.weather.temperature }}°C</span>
                <span class="weather-item">💨 {{ waypoint.weather.windSpeed }}m/s</span>
                <span class="weather-item">👁️ {{ waypoint.weather.visibility }}km</span>
                <span class="weather-warning" v-if="waypoint.weather.warning">
                  ⚠️ {{ waypoint.weather.warning }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 飞行参数设置 -->
      <div class="flight-params">
        <h4>⚙️ 飞行参数</h4>
        <div class="params-grid">
          <div class="param-group">
            <label>巡航速度 (m/s):</label>
            <input type="number" v-model="flightParams.cruiseSpeed" min="1" max="100" step="1" />
          </div>
          <div class="param-group">
            <label>最大爬升率 (m/s):</label>
            <input type="number" v-model="flightParams.maxClimbRate" min="0.1" max="10" step="0.1" />
          </div>
          <div class="param-group">
            <label>最大下降率 (m/s):</label>
            <input type="number" v-model="flightParams.maxDescentRate" min="0.1" max="10" step="0.1" />
          </div>
          <div class="param-group">
            <label>转弯半径 (m):</label>
            <input type="number" v-model="flightParams.turnRadius" min="10" max="1000" step="10" />
          </div>
          <div class="param-group">
            <label>安全间隔 (m):</label>
            <input type="number" v-model="flightParams.safetyMargin" min="10" max="500" step="10" />
          </div>
          <div class="param-group">
            <label>燃料消耗 (L/km):</label>
            <input type="number" v-model="flightParams.fuelConsumption" min="0.1" max="10" step="0.1" />
          </div>
        </div>
      </div>

      <!-- 风险评估 -->
      <div class="risk-assessment" v-if="activeRoute">
        <h4>⚠️ 风险评估</h4>
        <div class="risk-factors">
          <div class="risk-item" v-for="risk in riskFactors" :key="risk.type" :class="risk.level">
            <span class="risk-icon">{{ risk.icon }}</span>
            <span class="risk-name">{{ risk.name }}</span>
            <span class="risk-value">{{ risk.value }}</span>
            <span class="risk-level">{{ risk.level }}</span>
          </div>
        </div>
        
        <div class="risk-recommendations" v-if="recommendations.length > 0">
          <h5>💡 建议</h5>
          <ul>
            <li v-for="rec in recommendations" :key="rec">{{ rec }}</li>
          </ul>
        </div>
      </div>

      <!-- 保存的路径 -->
      <div class="saved-routes">
        <h4>💾 保存的路径</h4>
        <div class="routes-list">
          <div 
            v-for="route in savedRoutes" 
            :key="route.id"
            class="saved-route-item"
            @click="loadRoute(route)"
          >
            <div class="route-header">
              <span class="route-name">{{ route.name }}</span>
              <span class="route-date">{{ formatDate(route.createdAt) }}</span>
            </div>
            <div class="route-summary">
              <span>{{ route.waypoints.length }} 航点</span>
              <span>{{ route.distance.toFixed(1) }} km</span>
              <span>{{ route.estimatedTime }} 分钟</span>
            </div>
            <div class="route-actions">
              <button @click.stop="duplicateRoute(route)" class="btn btn-xs">复制</button>
              <button @click.stop="deleteRoute(route.id)" class="btn btn-xs btn-danger">删除</button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'

// 响应式数据
const activeRoute = ref(null)
const selectedWaypoint = ref(-1)
const savedRoutes = ref([])

// 飞行参数
const flightParams = ref({
  cruiseSpeed: 25,
  maxClimbRate: 3.0,
  maxDescentRate: 2.5,
  turnRadius: 100,
  safetyMargin: 50,
  fuelConsumption: 2.5
})

// 计算属性
const hasActiveRoute = computed(() => {
  return activeRoute.value && activeRoute.value.waypoints.length > 0
})

const totalDistance = computed(() => {
  if (!activeRoute.value || activeRoute.value.waypoints.length < 2) return 0
  
  let distance = 0
  const waypoints = activeRoute.value.waypoints
  
  for (let i = 1; i < waypoints.length; i++) {
    const prev = waypoints[i - 1]
    const curr = waypoints[i]
    distance += calculateDistance(prev, curr)
  }
  
  return distance
})

const estimatedTime = computed(() => {
  if (!activeRoute.value || totalDistance.value === 0) return 0
  return Math.round((totalDistance.value * 1000) / flightParams.value.cruiseSpeed / 60)
})

const averageAltitude = computed(() => {
  if (!activeRoute.value || activeRoute.value.waypoints.length === 0) return 0
  
  const sum = activeRoute.value.waypoints.reduce((acc, wp) => acc + wp.altitude, 0)
  return sum / activeRoute.value.waypoints.length
})

const riskLevel = computed(() => {
  const factors = riskFactors.value
  const highRisk = factors.filter(f => f.level === 'high').length
  const mediumRisk = factors.filter(f => f.level === 'medium').length
  
  if (highRisk > 0) return '高风险'
  if (mediumRisk > 1) return '中风险'
  return '低风险'
})

const riskLevelClass = computed(() => {
  const level = riskLevel.value
  if (level === '高风险') return 'risk-high'
  if (level === '中风险') return 'risk-medium'
  return 'risk-low'
})

const riskFactors = computed(() => {
  if (!activeRoute.value) return []
  
  return [
    {
      type: 'weather',
      name: '天气条件',
      icon: '🌤️',
      value: '良好',
      level: 'low'
    },
    {
      type: 'wind',
      name: '风速风向',
      icon: '💨',
      value: '8.2 m/s',
      level: 'medium'
    },
    {
      type: 'visibility',
      name: '能见度',
      icon: '👁️',
      value: '5.2 km',
      level: 'low'
    },
    {
      type: 'turbulence',
      name: '湍流强度',
      icon: '🌪️',
      value: '轻微',
      level: 'low'
    },
    {
      type: 'terrain',
      name: '地形障碍',
      icon: '🏔️',
      value: '无障碍',
      level: 'low'
    }
  ]
})

const recommendations = computed(() => {
  const recs = []
  
  if (riskLevel.value === '高风险') {
    recs.push('建议推迟飞行或选择替代路径')
  }
  
  if (averageAltitude.value < 200) {
    recs.push('建议提高飞行高度以避开地面障碍')
  }
  
  if (totalDistance.value > 50) {
    recs.push('长距离飞行，建议检查燃料储备')
  }
  
  return recs
})

// 方法
const newRoute = () => {
  activeRoute.value = {
    id: Date.now(),
    name: `路径_${new Date().toLocaleTimeString()}`,
    waypoints: [],
    createdAt: new Date()
  }
}

const addWaypoint = () => {
  if (!activeRoute.value) return
  
  const newWaypoint = {
    id: Date.now(),
    name: `航点_${activeRoute.value.waypoints.length + 1}`,
    latitude: 39.9042 + Math.random() * 0.01,
    longitude: 116.4074 + Math.random() * 0.01,
    altitude: 500 + Math.random() * 1000,
    speed: flightParams.value.cruiseSpeed,
    weather: generateMockWeather()
  }
  
  activeRoute.value.waypoints.push(newWaypoint)
}

const removeWaypoint = (index) => {
  if (!activeRoute.value) return
  activeRoute.value.waypoints.splice(index, 1)
  if (selectedWaypoint.value >= index) {
    selectedWaypoint.value = -1
  }
}

const selectWaypoint = (index) => {
  selectedWaypoint.value = selectedWaypoint.value === index ? -1 : index
}

const optimizeRoute = () => {
  console.log('优化路径')
  // 实现路径优化算法
}

const reverseRoute = () => {
  if (!activeRoute.value) return
  activeRoute.value.waypoints.reverse()
}

const saveRoute = () => {
  if (!activeRoute.value) return
  
  const routeToSave = {
    ...activeRoute.value,
    distance: totalDistance.value,
    estimatedTime: estimatedTime.value
  }
  
  const existingIndex = savedRoutes.value.findIndex(r => r.id === routeToSave.id)
  if (existingIndex >= 0) {
    savedRoutes.value[existingIndex] = routeToSave
  } else {
    savedRoutes.value.push(routeToSave)
  }
  
  console.log('路径已保存')
}

const loadRoute = (route) => {
  activeRoute.value = { ...route }
  selectedWaypoint.value = -1
}

const duplicateRoute = (route) => {
  const duplicated = {
    ...route,
    id: Date.now(),
    name: `${route.name}_副本`,
    createdAt: new Date()
  }
  savedRoutes.value.push(duplicated)
}

const deleteRoute = (routeId) => {
  savedRoutes.value = savedRoutes.value.filter(r => r.id !== routeId)
}

const clearRoute = () => {
  activeRoute.value = null
  selectedWaypoint.value = -1
}

const calculateDistance = (point1, point2) => {
  // 简化的距离计算（实际应使用更精确的地球距离公式）
  const dx = point2.longitude - point1.longitude
  const dy = point2.latitude - point1.latitude
  const dz = (point2.altitude - point1.altitude) / 1000 // 转换为km
  
  return Math.sqrt(dx * dx * 111 * 111 + dy * dy * 111 * 111 + dz * dz)
}

const generateMockWeather = () => {
  return {
    temperature: Math.round(15 + Math.random() * 10),
    windSpeed: Math.round(Math.random() * 15 * 10) / 10,
    visibility: Math.round(Math.random() * 10 * 10) / 10,
    warning: Math.random() > 0.8 ? '强风预警' : null
  }
}

const formatDate = (date) => {
  return new Date(date).toLocaleDateString()
}

// 暴露给父组件
defineExpose({
  newRoute,
  saveRoute,
  clearRoute,
  activeRoute
})
</script>

<style scoped>
.flight-planner {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #2c3e50;
  color: white;
}

.planner-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background: #34495e;
  border-bottom: 1px solid #3498db;
}

.planner-header h3 {
  margin: 0;
  color: #3498db;
}

.planner-actions {
  display: flex;
  gap: 10px;
}

.planner-content {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
}

.route-info,
.waypoints-section,
.flight-params,
.risk-assessment,
.saved-routes {
  margin-bottom: 25px;
  padding: 15px;
  background: rgba(52, 73, 94, 0.3);
  border-radius: 8px;
  border: 1px solid #34495e;
}

.route-info h4,
.waypoints-section h4,
.flight-params h4,
.risk-assessment h4,
.saved-routes h4 {
  margin: 0 0 15px 0;
  color: #3498db;
  font-size: 1rem;
}

.info-grid,
.params-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px;
}

.info-item,
.param-group {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  background: rgba(26, 26, 26, 0.5);
  border-radius: 4px;
}

.label {
  color: #bdc3c7;
  font-size: 0.9rem;
}

.value {
  color: #ffffff;
  font-weight: 600;
}

.value.risk-high {
  color: #e74c3c;
}

.value.risk-medium {
  color: #f39c12;
}

.value.risk-low {
  color: #27ae60;
}

.route-name-input {
  background: transparent;
  border: 1px solid #34495e;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  width: 150px;
}

.waypoints-controls {
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
}

.waypoints-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
  max-height: 400px;
  overflow-y: auto;
}

.waypoint-item {
  padding: 12px;
  background: rgba(26, 26, 26, 0.5);
  border-radius: 6px;
  border: 1px solid #34495e;
  cursor: pointer;
  transition: all 0.2s;
}

.waypoint-item:hover {
  border-color: #3498db;
}

.waypoint-item.selected {
  border-color: #3498db;
  background: rgba(52, 152, 219, 0.1);
}

.waypoint-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 10px;
}

.waypoint-number {
  width: 24px;
  height: 24px;
  background: #3498db;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
  font-weight: 600;
}

.waypoint-name {
  flex: 1;
  background: transparent;
  border: 1px solid #34495e;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
}

.remove-btn {
  width: 24px;
  height: 24px;
  background: #e74c3c;
  color: white;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  font-size: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.waypoint-details {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px;
  margin-bottom: 10px;
}

.coordinate-group {
  display: flex;
  align-items: center;
  gap: 5px;
}

.coordinate-group label {
  font-size: 0.8rem;
  color: #bdc3c7;
  min-width: 40px;
}

.coordinate-group input {
  flex: 1;
  background: transparent;
  border: 1px solid #34495e;
  color: white;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 0.8rem;
}

.unit {
  font-size: 0.8rem;
  color: #95a5a6;
}

.waypoint-weather {
  border-top: 1px solid #34495e;
  padding-top: 8px;
}

.weather-summary {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

.weather-item {
  font-size: 0.8rem;
  color: #bdc3c7;
}

.weather-warning {
  color: #e74c3c;
  font-weight: 600;
}

.param-group label {
  color: #bdc3c7;
  font-size: 0.9rem;
}

.param-group input {
  width: 80px;
  background: transparent;
  border: 1px solid #34495e;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  text-align: center;
}

.risk-factors {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 15px;
}

.risk-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 8px;
  border-radius: 4px;
  border-left: 4px solid;
}

.risk-item.low {
  background: rgba(39, 174, 96, 0.1);
  border-left-color: #27ae60;
}

.risk-item.medium {
  background: rgba(243, 156, 18, 0.1);
  border-left-color: #f39c12;
}

.risk-item.high {
  background: rgba(231, 76, 60, 0.1);
  border-left-color: #e74c3c;
}

.risk-icon {
  font-size: 1.2rem;
}

.risk-name {
  flex: 1;
  font-size: 0.9rem;
}

.risk-value {
  font-weight: 600;
  margin-right: 10px;
}

.risk-level {
  font-size: 0.8rem;
  padding: 2px 8px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.1);
}

.risk-recommendations h5 {
  margin: 0 0 10px 0;
  color: #f39c12;
}

.risk-recommendations ul {
  margin: 0;
  padding-left: 20px;
}

.risk-recommendations li {
  margin-bottom: 5px;
  font-size: 0.9rem;
  color: #bdc3c7;
}

.routes-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
  max-height: 300px;
  overflow-y: auto;
}

.saved-route-item {
  padding: 12px;
  background: rgba(26, 26, 26, 0.5);
  border-radius: 6px;
  border: 1px solid #34495e;
  cursor: pointer;
  transition: all 0.2s;
}

.saved-route-item:hover {
  border-color: #3498db;
  background: rgba(52, 152, 219, 0.1);
}

.route-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.route-name {
  font-weight: 600;
  color: #ffffff;
}

.route-date {
  font-size: 0.8rem;
  color: #95a5a6;
}

.route-summary {
  display: flex;
  gap: 15px;
  font-size: 0.9rem;
  color: #bdc3c7;
  margin-bottom: 8px;
}

.route-actions {
  display: flex;
  gap: 8px;
}

.btn {
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 500;
  transition: all 0.2s;
}

.btn-primary {
  background: #3498db;
  color: white;
}

.btn-success {
  background: #27ae60;
  color: white;
}

.btn-danger {
  background: #e74c3c;
  color: white;
}

.btn-sm {
  padding: 4px 8px;
  font-size: 0.8rem;
}

.btn-xs {
  padding: 2px 6px;
  font-size: 0.7rem;
}

.btn:hover {
  opacity: 0.9;
  transform: translateY(-1px);
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: rgba(44, 62, 80, 0.3);
}

::-webkit-scrollbar-thumb {
  background: #34495e;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #3498db;
}
</style>
