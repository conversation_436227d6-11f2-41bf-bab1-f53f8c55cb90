<template>
  <div>
    <h1>Cesium 地球测试</h1>
    <div id="cesiumContainer" class="cesium-container"></div>
  </div>
</template>

<script setup>
import { onMounted, onUnmounted } from 'vue'

let viewer = null

onMounted(async () => {
  try {
    // 动态导入Cesium
    const Cesium = await import('cesium')
    
    // 创建最简单的Cesium Viewer
    viewer = new Cesium.Viewer('cesiumContainer')
    
    console.log('Cesium地球创建成功！')
  } catch (error) {
    console.error('Cesium初始化失败:', error)
  }
})

onUnmounted(() => {
  if (viewer) {
    viewer.destroy()
    viewer = null
  }
})
</script>

<style scoped>
.cesium-container {
  width: 100%;
  height: 80vh;
  margin: 20px 0;
}

h1 {
  text-align: center;
  color: #333;
  margin: 20px 0;
}
</style>
