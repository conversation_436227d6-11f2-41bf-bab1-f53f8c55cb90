<template>
  <div class="cesium-viewer">
    <div class="viewer-container" ref="viewerContainer">
      <!-- 模拟的3D地球视图 -->
      <div class="mock-earth">
        <div class="earth-sphere" :style="earthTransform">
          <div class="earth-surface"></div>
          <div class="atmosphere"></div>
        </div>
        
        <!-- 气象数据层 -->
        <div class="weather-layers">
          <div 
            v-for="layer in visibleLayers" 
            :key="layer.id"
            class="weather-layer"
            :class="layer.type"
            :style="{ opacity: layer.opacity }"
          >
            <div class="layer-content">
              {{ layer.name }}
            </div>
          </div>
        </div>
        
        <!-- 预警区域 -->
        <div class="warning-areas">
          <div 
            v-for="warning in warnings" 
            :key="warning.id"
            class="warning-area"
            :class="warning.level"
            :style="getWarningStyle(warning)"
          >
            <div class="warning-label">
              {{ warning.icon }} {{ warning.title }}
            </div>
          </div>
        </div>
        
        <!-- 飞行路径 -->
        <div class="flight-paths" v-if="flightPath.length > 0">
          <svg class="path-svg" viewBox="0 0 400 400">
            <path 
              :d="getPathData()" 
              stroke="#3498db" 
              stroke-width="3" 
              fill="none"
              stroke-dasharray="5,5"
            >
              <animate 
                attributeName="stroke-dashoffset" 
                values="0;10" 
                dur="1s" 
                repeatCount="indefinite"
              />
            </path>
            <circle 
              v-for="(point, index) in flightPath" 
              :key="index"
              :cx="point.x" 
              :cy="point.y" 
              r="4" 
              fill="#e74c3c"
            />
          </svg>
        </div>
      </div>
      
      <!-- 控制界面 -->
      <div class="viewer-controls">
        <div class="control-group">
          <button @click="resetView" class="control-btn" title="重置视角">
            🏠
          </button>
          <button @click="toggleRotation" class="control-btn" title="自动旋转">
            {{ isRotating ? '⏸️' : '🔄' }}
          </button>
          <button @click="toggleFullscreen" class="control-btn" title="全屏">
            ⛶
          </button>
        </div>
        
        <div class="zoom-controls">
          <button @click="zoomIn" class="zoom-btn">+</button>
          <button @click="zoomOut" class="zoom-btn">-</button>
        </div>
      </div>
      
      <!-- 坐标显示 -->
      <div class="coordinates-display">
        <span>{{ currentCoordinates.lat.toFixed(4) }}°N</span>
        <span>{{ currentCoordinates.lon.toFixed(4) }}°E</span>
        <span>{{ currentCoordinates.alt }}m</span>
      </div>
      
      <!-- 加载状态 -->
      <div v-if="isLoading" class="loading-overlay">
        <div class="loading-spinner"></div>
        <p>正在加载气象数据...</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed, watch } from 'vue'

// Props
const props = defineProps({
  weatherLayers: {
    type: Array,
    default: () => []
  },
  warnings: {
    type: Array,
    default: () => []
  },
  flightPath: {
    type: Array,
    default: () => []
  }
})

// 响应式数据
const viewerContainer = ref(null)
const isLoading = ref(false)
const isRotating = ref(false)
const rotationAngle = ref(0)
const zoomLevel = ref(1)
const currentCoordinates = ref({
  lat: 39.9042,
  lon: 116.4074,
  alt: 1000
})

let rotationInterval = null

// 计算属性
const earthTransform = computed(() => {
  return {
    transform: `rotateY(${rotationAngle.value}deg) scale(${zoomLevel.value})`
  }
})

const visibleLayers = computed(() => {
  return props.weatherLayers.filter(layer => layer.visible)
})

// 生命周期
onMounted(() => {
  initializeViewer()
  setupEventListeners()
})

onUnmounted(() => {
  cleanup()
})

// 方法
const initializeViewer = () => {
  console.log('初始化简化Cesium查看器...')
  isLoading.value = true
  
  // 模拟加载时间
  setTimeout(() => {
    isLoading.value = false
    console.log('查看器初始化完成')
  }, 1000)
}

const setupEventListeners = () => {
  if (!viewerContainer.value) return
  
  // 鼠标事件
  viewerContainer.value.addEventListener('mousemove', handleMouseMove)
  viewerContainer.value.addEventListener('wheel', handleWheel)
  viewerContainer.value.addEventListener('click', handleClick)
}

const handleMouseMove = (event) => {
  const rect = viewerContainer.value.getBoundingClientRect()
  const x = event.clientX - rect.left
  const y = event.clientY - rect.top
  
  // 模拟坐标转换
  const lat = 39.9042 + (y - rect.height / 2) / rect.height * 2
  const lon = 116.4074 + (x - rect.width / 2) / rect.width * 2
  
  currentCoordinates.value = {
    lat: Math.max(-90, Math.min(90, lat)),
    lon: Math.max(-180, Math.min(180, lon)),
    alt: Math.round(Math.random() * 3000)
  }
}

const handleWheel = (event) => {
  event.preventDefault()
  const delta = event.deltaY > 0 ? -0.1 : 0.1
  zoomLevel.value = Math.max(0.5, Math.min(3, zoomLevel.value + delta))
}

const handleClick = (event) => {
  const rect = viewerContainer.value.getBoundingClientRect()
  const x = event.clientX - rect.left
  const y = event.clientY - rect.top
  
  console.log('点击位置:', { x, y })
  // 可以在这里添加点击事件处理逻辑
}

const resetView = () => {
  rotationAngle.value = 0
  zoomLevel.value = 1
  currentCoordinates.value = {
    lat: 39.9042,
    lon: 116.4074,
    alt: 1000
  }
}

const toggleRotation = () => {
  isRotating.value = !isRotating.value
  
  if (isRotating.value) {
    rotationInterval = setInterval(() => {
      rotationAngle.value += 1
      if (rotationAngle.value >= 360) {
        rotationAngle.value = 0
      }
    }, 50)
  } else {
    if (rotationInterval) {
      clearInterval(rotationInterval)
      rotationInterval = null
    }
  }
}

const toggleFullscreen = () => {
  if (!document.fullscreenElement) {
    viewerContainer.value.requestFullscreen()
  } else {
    document.exitFullscreen()
  }
}

const zoomIn = () => {
  zoomLevel.value = Math.min(3, zoomLevel.value + 0.2)
}

const zoomOut = () => {
  zoomLevel.value = Math.max(0.5, zoomLevel.value - 0.2)
}

const getWarningStyle = (warning) => {
  // 模拟预警区域位置
  const x = Math.random() * 80 + 10
  const y = Math.random() * 80 + 10
  
  return {
    left: `${x}%`,
    top: `${y}%`
  }
}

const getPathData = () => {
  if (props.flightPath.length < 2) return ''
  
  let path = `M ${props.flightPath[0].x} ${props.flightPath[0].y}`
  for (let i = 1; i < props.flightPath.length; i++) {
    path += ` L ${props.flightPath[i].x} ${props.flightPath[i].y}`
  }
  return path
}

const cleanup = () => {
  if (rotationInterval) {
    clearInterval(rotationInterval)
  }
  
  if (viewerContainer.value) {
    viewerContainer.value.removeEventListener('mousemove', handleMouseMove)
    viewerContainer.value.removeEventListener('wheel', handleWheel)
    viewerContainer.value.removeEventListener('click', handleClick)
  }
}

// 暴露方法给父组件
defineExpose({
  resetView,
  toggleRotation,
  zoomIn,
  zoomOut
})
</script>

<style scoped>
.cesium-viewer {
  width: 100%;
  height: 100%;
  position: relative;
  background: radial-gradient(ellipse at center, #1e3c72 0%, #0a0a0a 100%);
  overflow: hidden;
}

.viewer-container {
  width: 100%;
  height: 100%;
  position: relative;
  cursor: grab;
}

.viewer-container:active {
  cursor: grabbing;
}

.mock-earth {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 300px;
  height: 300px;
  perspective: 1000px;
}

.earth-sphere {
  width: 100%;
  height: 100%;
  position: relative;
  transform-style: preserve-3d;
  transition: transform 0.1s ease;
}

.earth-surface {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: 
    radial-gradient(circle at 30% 30%, #4a90e2, #2c5aa0),
    linear-gradient(45deg, #228b22, #32cd32, #228b22, #8fbc8f);
  background-size: 100% 100%, 50px 50px;
  position: relative;
  box-shadow: 
    inset -20px -20px 50px rgba(0, 0, 0, 0.5),
    0 0 50px rgba(74, 144, 226, 0.3);
}

.earth-surface::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 50%;
  background: 
    radial-gradient(circle at 20% 20%, transparent 30%, rgba(255, 255, 255, 0.1) 50%),
    linear-gradient(45deg, transparent 40%, rgba(139, 69, 19, 0.3) 60%);
  background-size: 80px 80px, 120px 120px;
}

.atmosphere {
  position: absolute;
  top: -10px;
  left: -10px;
  right: -10px;
  bottom: -10px;
  border-radius: 50%;
  background: radial-gradient(circle, transparent 70%, rgba(135, 206, 235, 0.3) 100%);
  pointer-events: none;
}

.weather-layers {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.weather-layer {
  position: absolute;
  top: 10%;
  left: 10%;
  right: 10%;
  bottom: 10%;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: opacity 0.3s ease;
}

.weather-layer.heatmap {
  background: radial-gradient(circle, rgba(255, 0, 0, 0.3), transparent);
}

.weather-layer.vector {
  background: repeating-linear-gradient(
    45deg,
    transparent,
    transparent 10px,
    rgba(0, 255, 255, 0.2) 10px,
    rgba(0, 255, 255, 0.2) 20px
  );
}

.layer-content {
  color: white;
  font-size: 0.8rem;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
}

.warning-areas {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.warning-area {
  position: absolute;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: pulse 2s infinite;
}

.warning-area.warning {
  background: rgba(231, 76, 60, 0.3);
  border: 2px solid #e74c3c;
}

.warning-area.caution {
  background: rgba(243, 156, 18, 0.3);
  border: 2px solid #f39c12;
}

.warning-label {
  color: white;
  font-size: 0.7rem;
  text-align: center;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
}

.flight-paths {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.path-svg {
  width: 100%;
  height: 100%;
}

.viewer-controls {
  position: absolute;
  top: 20px;
  right: 20px;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.control-group {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.control-btn,
.zoom-btn {
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 50%;
  background: rgba(44, 62, 80, 0.9);
  color: white;
  font-size: 1.2rem;
  cursor: pointer;
  transition: all 0.2s ease;
  backdrop-filter: blur(10px);
}

.control-btn:hover,
.zoom-btn:hover {
  background: rgba(52, 152, 219, 0.9);
  transform: scale(1.1);
}

.zoom-controls {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.coordinates-display {
  position: absolute;
  bottom: 20px;
  left: 20px;
  background: rgba(44, 62, 80, 0.9);
  color: white;
  padding: 10px 15px;
  border-radius: 8px;
  font-family: monospace;
  font-size: 0.9rem;
  backdrop-filter: blur(10px);
  display: flex;
  gap: 15px;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 4px solid #34495e;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes pulse {
  0%, 100% { opacity: 0.7; transform: scale(1); }
  50% { opacity: 1; transform: scale(1.1); }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .mock-earth {
    width: 250px;
    height: 250px;
  }
  
  .viewer-controls {
    top: 10px;
    right: 10px;
  }
  
  .coordinates-display {
    bottom: 10px;
    left: 10px;
    font-size: 0.8rem;
    gap: 10px;
  }
}
</style>
