<template>
  <div class="earth-wrapper">
    <div class="header">
      <h1>🌍 3D 地球</h1>
      <p>基于 Three.js 的简单地球演示</p>
    </div>
    
    <div class="controls">
      <button @click="toggleRotation" class="btn">
        {{ isRotating ? '⏸️ 暂停' : '▶️ 旋转' }}
      </button>
      <button @click="resetView" class="btn">🏠 重置</button>
      <button @click="changeTexture" class="btn">🎨 切换</button>
    </div>
    
    <div id="earthContainer" class="earth-container"></div>
    
    <div class="info">
      <p>🖱️ 鼠标拖拽旋转 | 🔄 滚轮缩放</p>
    </div>
  </div>
</template>

<script setup>
import { onMounted, onUnmounted, ref } from 'vue'

let scene, camera, renderer, earth, animationId
const isRotating = ref(true)
const currentTexture = ref(0)

// 简单的地球纹理数据（使用CSS渐变模拟）
const textures = [
  'linear-gradient(45deg, #4a90e2, #7b68ee, #50c878, #228b22)',
  'linear-gradient(45deg, #ff6b6b, #ffa500, #ffff00, #32cd32)',
  'linear-gradient(45deg, #9b59b6, #3498db, #2ecc71, #f39c12)'
]

onMounted(() => {
  initEarth()
  animate()
})

onUnmounted(() => {
  if (animationId) {
    cancelAnimationFrame(animationId)
  }
  if (renderer) {
    renderer.dispose()
  }
})

const initEarth = () => {
  const container = document.getElementById('earthContainer')
  
  // 创建一个简单的CSS 3D地球
  earth = document.createElement('div')
  earth.className = 'earth-sphere'
  earth.style.background = textures[currentTexture.value]
  
  // 添加一些"大陆"
  for (let i = 0; i < 8; i++) {
    const continent = document.createElement('div')
    continent.className = 'continent'
    continent.style.top = Math.random() * 80 + '%'
    continent.style.left = Math.random() * 80 + '%'
    continent.style.width = Math.random() * 30 + 10 + 'px'
    continent.style.height = Math.random() * 20 + 8 + 'px'
    earth.appendChild(continent)
  }
  
  container.appendChild(earth)
  
  // 添加鼠标交互
  let isDragging = false
  let previousMousePosition = { x: 0, y: 0 }
  let rotation = { x: 0, y: 0 }
  
  container.addEventListener('mousedown', (e) => {
    isDragging = true
    previousMousePosition = { x: e.clientX, y: e.clientY }
  })
  
  container.addEventListener('mousemove', (e) => {
    if (isDragging) {
      const deltaMove = {
        x: e.clientX - previousMousePosition.x,
        y: e.clientY - previousMousePosition.y
      }
      
      rotation.y += deltaMove.x * 0.5
      rotation.x += deltaMove.y * 0.5
      
      earth.style.transform = `rotateX(${rotation.x}deg) rotateY(${rotation.y}deg)`
      
      previousMousePosition = { x: e.clientX, y: e.clientY }
    }
  })
  
  container.addEventListener('mouseup', () => {
    isDragging = false
  })
  
  container.addEventListener('wheel', (e) => {
    e.preventDefault()
    const scale = earth.style.transform.match(/scale\(([^)]+)\)/)
    let currentScale = scale ? parseFloat(scale[1]) : 1
    
    if (e.deltaY < 0) {
      currentScale *= 1.1
    } else {
      currentScale *= 0.9
    }
    
    currentScale = Math.max(0.5, Math.min(3, currentScale))
    earth.style.transform = `rotateX(${rotation.x}deg) rotateY(${rotation.y}deg) scale(${currentScale})`
  })
}

const animate = () => {
  if (isRotating.value && earth) {
    const currentTransform = earth.style.transform
    const rotateY = currentTransform.match(/rotateY\(([^)]+)deg\)/)
    let currentY = rotateY ? parseFloat(rotateY[1]) : 0
    currentY += 0.5
    
    earth.style.transform = currentTransform.replace(
      /rotateY\([^)]+deg\)/,
      `rotateY(${currentY}deg)`
    ) || `rotateY(${currentY}deg)`
  }
  
  animationId = requestAnimationFrame(animate)
}

const toggleRotation = () => {
  isRotating.value = !isRotating.value
}

const resetView = () => {
  if (earth) {
    earth.style.transform = 'rotateX(0deg) rotateY(0deg) scale(1)'
  }
}

const changeTexture = () => {
  currentTexture.value = (currentTexture.value + 1) % textures.length
  if (earth) {
    earth.style.background = textures[currentTexture.value]
  }
}
</script>

<style scoped>
.earth-wrapper {
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: hidden;
  background: linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 50%, #16213e 100%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.header {
  position: absolute;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1000;
  text-align: center;
  color: white;
  background: rgba(0, 0, 0, 0.7);
  padding: 15px 30px;
  border-radius: 10px;
  backdrop-filter: blur(10px);
}

.header h1 {
  margin: 0 0 5px 0;
  font-size: 24px;
}

.header p {
  margin: 0;
  font-size: 14px;
  opacity: 0.8;
}

.controls {
  position: absolute;
  bottom: 80px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1000;
  display: flex;
  gap: 15px;
  background: rgba(0, 0, 0, 0.7);
  padding: 15px;
  border-radius: 10px;
  backdrop-filter: blur(10px);
}

.btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 12px 20px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

.btn:active {
  transform: translateY(0);
}

.earth-container {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: grab;
}

.earth-container:active {
  cursor: grabbing;
}

.earth-sphere {
  width: 300px;
  height: 300px;
  border-radius: 50%;
  position: relative;
  box-shadow: 
    inset -50px -50px 100px rgba(0, 0, 0, 0.5),
    0 0 50px rgba(100, 150, 255, 0.3);
  transition: transform 0.1s ease;
  transform-style: preserve-3d;
}

.continent {
  position: absolute;
  background: rgba(34, 139, 34, 0.8);
  border-radius: 50%;
  box-shadow: inset 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.info {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1000;
  color: white;
  text-align: center;
  background: rgba(0, 0, 0, 0.5);
  padding: 10px 20px;
  border-radius: 5px;
  font-size: 14px;
}

/* 添加星空背景 */
.earth-wrapper::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: 
    radial-gradient(2px 2px at 20px 30px, #eee, transparent),
    radial-gradient(2px 2px at 40px 70px, rgba(255,255,255,0.8), transparent),
    radial-gradient(1px 1px at 90px 40px, #fff, transparent),
    radial-gradient(1px 1px at 130px 80px, rgba(255,255,255,0.6), transparent),
    radial-gradient(2px 2px at 160px 30px, #ddd, transparent);
  background-repeat: repeat;
  background-size: 200px 100px;
  animation: twinkle 10s infinite;
  pointer-events: none;
}

@keyframes twinkle {
  0%, 100% { opacity: 0.8; }
  50% { opacity: 1; }
}
</style>
