<template>
  <div class="weather-visualization">
    <div id="cesiumContainer" class="cesium-container"></div>
    
    <!-- 图层控制面板 -->
    <div class="layer-control">
      <h4>🗂️ 图层控制</h4>
      <div class="layer-list">
        <div 
          v-for="layer in weatherLayers" 
          :key="layer.id"
          class="layer-item"
          :class="{ active: layer.visible }"
          @click="toggleLayer(layer.id)"
        >
          <span class="layer-icon">{{ layer.icon }}</span>
          <span class="layer-name">{{ layer.name }}</span>
          <div class="layer-opacity" v-if="layer.visible">
            <input 
              type="range" 
              v-model="layer.opacity" 
              min="0" 
              max="1" 
              step="0.1"
              @input="updateLayerOpacity(layer.id, layer.opacity)"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 时间控制器 -->
    <div class="time-control">
      <h4>⏰ 时间控制</h4>
      <div class="time-slider">
        <button @click="playAnimation" class="time-btn">
          {{ isPlaying ? '⏸️' : '▶️' }}
        </button>
        <input 
          type="range" 
          v-model="currentTimeIndex" 
          min="0" 
          :max="timeSteps.length - 1" 
          @input="updateTimeStep"
        />
        <span class="time-display">{{ currentTimeDisplay }}</span>
      </div>
      <div class="time-speed">
        <label>播放速度:</label>
        <select v-model="animationSpeed">
          <option value="0.5">0.5x</option>
          <option value="1">1x</option>
          <option value="2">2x</option>
          <option value="4">4x</option>
        </select>
      </div>
    </div>

    <!-- 高度切片控制 -->
    <div class="altitude-slicer">
      <h4>📏 高度切片</h4>
      <div class="altitude-controls">
        <div class="altitude-range">
          <label>显示范围:</label>
          <input type="number" v-model="altitudeSlice.min" min="0" max="3000" step="100" />
          <span>-</span>
          <input type="number" v-model="altitudeSlice.max" min="0" max="3000" step="100" />
          <span>米</span>
        </div>
        <div class="slice-thickness">
          <label>切片厚度:</label>
          <input type="number" v-model="altitudeSlice.thickness" min="50" max="500" step="50" />
          <span>米</span>
        </div>
      </div>
    </div>

    <!-- 颜色图例 -->
    <div class="color-legend" v-if="activeLegend">
      <h4>{{ activeLegend.title }}</h4>
      <div class="legend-scale">
        <div class="legend-bar" :style="{ background: activeLegend.gradient }"></div>
        <div class="legend-labels">
          <span>{{ activeLegend.min }}</span>
          <span>{{ activeLegend.max }}</span>
        </div>
        <div class="legend-unit">{{ activeLegend.unit }}</div>
      </div>
    </div>

    <!-- 测量工具 -->
    <div class="measurement-tools">
      <h4>📐 测量工具</h4>
      <div class="tool-buttons">
        <button @click="startDistanceMeasurement" class="tool-btn" :class="{ active: measurementMode === 'distance' }">
          📏 距离
        </button>
        <button @click="startAreaMeasurement" class="tool-btn" :class="{ active: measurementMode === 'area' }">
          📐 面积
        </button>
        <button @click="startVolumeMeasurement" class="tool-btn" :class="{ active: measurementMode === 'volume' }">
          📦 体积
        </button>
        <button @click="clearMeasurements" class="tool-btn">
          🗑️ 清除
        </button>
      </div>
      <div class="measurement-results" v-if="measurementResults.length > 0">
        <div v-for="result in measurementResults" :key="result.id" class="result-item">
          <span>{{ result.type }}: {{ result.value }} {{ result.unit }}</span>
          <button @click="removeMeasurement(result.id)" class="remove-btn">×</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed, watch } from 'vue'

// 响应式数据
const isPlaying = ref(false)
const currentTimeIndex = ref(0)
const animationSpeed = ref(1)
const measurementMode = ref('')
const measurementResults = ref([])

// 气象图层配置
const weatherLayers = ref([
  {
    id: 'temperature',
    name: '温度分布',
    icon: '🌡️',
    visible: true,
    opacity: 0.7,
    type: 'heatmap'
  },
  {
    id: 'wind',
    name: '风场矢量',
    icon: '💨',
    visible: true,
    opacity: 0.8,
    type: 'vector'
  },
  {
    id: 'precipitation',
    name: '降水强度',
    icon: '🌧️',
    visible: false,
    opacity: 0.6,
    type: 'heatmap'
  },
  {
    id: 'visibility',
    name: '能见度',
    icon: '🌫️',
    visible: true,
    opacity: 0.5,
    type: 'heatmap'
  },
  {
    id: 'turbulence',
    name: '湍流强度',
    icon: '🌪️',
    visible: false,
    opacity: 0.7,
    type: 'volume'
  },
  {
    id: 'clouds',
    name: '云层分布',
    icon: '☁️',
    visible: false,
    opacity: 0.4,
    type: 'volume'
  }
])

// 时间步长
const timeSteps = ref([
  '2024-01-15 14:00',
  '2024-01-15 14:30',
  '2024-01-15 15:00',
  '2024-01-15 15:30',
  '2024-01-15 16:00',
  '2024-01-15 16:30',
  '2024-01-15 17:00',
  '2024-01-15 17:30',
  '2024-01-15 18:00'
])

// 高度切片设置
const altitudeSlice = ref({
  min: 0,
  max: 3000,
  thickness: 200
})

// 计算属性
const currentTimeDisplay = computed(() => {
  return timeSteps.value[currentTimeIndex.value] || ''
})

const activeLegend = computed(() => {
  const activeLayer = weatherLayers.value.find(layer => layer.visible)
  if (!activeLayer) return null

  const legends = {
    temperature: {
      title: '温度 (°C)',
      min: '-20',
      max: '40',
      unit: '°C',
      gradient: 'linear-gradient(to right, #0000ff, #00ffff, #00ff00, #ffff00, #ff0000)'
    },
    wind: {
      title: '风速 (m/s)',
      min: '0',
      max: '30',
      unit: 'm/s',
      gradient: 'linear-gradient(to right, #ffffff, #87ceeb, #4169e1, #0000cd, #000080)'
    },
    precipitation: {
      title: '降水强度 (mm/h)',
      min: '0',
      max: '50',
      unit: 'mm/h',
      gradient: 'linear-gradient(to right, #ffffff, #87ceeb, #4169e1, #0000cd, #8b0000)'
    },
    visibility: {
      title: '能见度 (km)',
      min: '0',
      max: '10',
      unit: 'km',
      gradient: 'linear-gradient(to right, #8b0000, #ff4500, #ffa500, #ffff00, #ffffff)'
    },
    turbulence: {
      title: '湍流强度',
      min: '轻微',
      max: '强烈',
      unit: '',
      gradient: 'linear-gradient(to right, #00ff00, #ffff00, #ffa500, #ff0000, #8b0000)'
    }
  }

  return legends[activeLayer.id] || null
})

// 生命周期
onMounted(() => {
  initializeVisualization()
})

onUnmounted(() => {
  cleanup()
})

// 监听器
watch(altitudeSlice, (newValue) => {
  updateAltitudeSlice(newValue)
}, { deep: true })

// 方法
const initializeVisualization = async () => {
  try {
    // 初始化Cesium和气象数据可视化
    console.log('初始化气象可视化...')
    await loadWeatherData()
    setupEventHandlers()
  } catch (error) {
    console.error('可视化初始化失败:', error)
  }
}

const loadWeatherData = async () => {
  // 模拟加载气象数据
  console.log('加载气象数据...')
  return new Promise(resolve => {
    setTimeout(resolve, 1000)
  })
}

const setupEventHandlers = () => {
  // 设置鼠标事件处理器
  console.log('设置事件处理器...')
}

const toggleLayer = (layerId) => {
  const layer = weatherLayers.value.find(l => l.id === layerId)
  if (layer) {
    layer.visible = !layer.visible
    updateLayerVisibility(layerId, layer.visible)
  }
}

const updateLayerVisibility = (layerId, visible) => {
  console.log(`更新图层 ${layerId} 可见性:`, visible)
  // 实际的图层显示/隐藏逻辑
}

const updateLayerOpacity = (layerId, opacity) => {
  console.log(`更新图层 ${layerId} 透明度:`, opacity)
  // 实际的透明度更新逻辑
}

const playAnimation = () => {
  isPlaying.value = !isPlaying.value
  if (isPlaying.value) {
    startTimeAnimation()
  } else {
    stopTimeAnimation()
  }
}

let animationInterval = null

const startTimeAnimation = () => {
  const interval = 1000 / animationSpeed.value
  animationInterval = setInterval(() => {
    if (currentTimeIndex.value < timeSteps.value.length - 1) {
      currentTimeIndex.value++
      updateTimeStep()
    } else {
      currentTimeIndex.value = 0
    }
  }, interval)
}

const stopTimeAnimation = () => {
  if (animationInterval) {
    clearInterval(animationInterval)
    animationInterval = null
  }
}

const updateTimeStep = () => {
  console.log('更新时间步长:', currentTimeDisplay.value)
  // 更新气象数据显示
}

const updateAltitudeSlice = (sliceConfig) => {
  console.log('更新高度切片:', sliceConfig)
  // 更新3D可视化的高度切片
}

const startDistanceMeasurement = () => {
  measurementMode.value = measurementMode.value === 'distance' ? '' : 'distance'
  console.log('开始距离测量')
}

const startAreaMeasurement = () => {
  measurementMode.value = measurementMode.value === 'area' ? '' : 'area'
  console.log('开始面积测量')
}

const startVolumeMeasurement = () => {
  measurementMode.value = measurementMode.value === 'volume' ? '' : 'volume'
  console.log('开始体积测量')
}

const clearMeasurements = () => {
  measurementResults.value = []
  measurementMode.value = ''
  console.log('清除所有测量')
}

const removeMeasurement = (id) => {
  measurementResults.value = measurementResults.value.filter(r => r.id !== id)
}

const cleanup = () => {
  stopTimeAnimation()
  // 清理其他资源
}

// 暴露给父组件的方法
defineExpose({
  toggleLayer,
  updateTimeStep,
  clearMeasurements
})
</script>

<style scoped>
.weather-visualization {
  position: relative;
  width: 100%;
  height: 100%;
}

.cesium-container {
  width: 100%;
  height: 100%;
}

.layer-control,
.time-control,
.altitude-slicer,
.color-legend,
.measurement-tools {
  position: absolute;
  background: rgba(44, 62, 80, 0.95);
  border: 1px solid #34495e;
  border-radius: 8px;
  padding: 15px;
  color: white;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.layer-control {
  top: 20px;
  left: 20px;
  width: 250px;
  max-height: 400px;
  overflow-y: auto;
}

.time-control {
  bottom: 20px;
  left: 20px;
  width: 300px;
}

.altitude-slicer {
  top: 20px;
  right: 20px;
  width: 250px;
}

.color-legend {
  top: 300px;
  right: 20px;
  width: 200px;
}

.measurement-tools {
  bottom: 20px;
  right: 20px;
  width: 250px;
}

h4 {
  margin: 0 0 15px 0;
  color: #3498db;
  font-size: 1rem;
  border-bottom: 1px solid #34495e;
  padding-bottom: 8px;
}

.layer-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.layer-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 8px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
  border: 1px solid transparent;
}

.layer-item:hover {
  background: rgba(52, 73, 94, 0.5);
}

.layer-item.active {
  background: rgba(52, 152, 219, 0.2);
  border-color: #3498db;
}

.layer-icon {
  font-size: 1.2rem;
  width: 20px;
  text-align: center;
}

.layer-name {
  flex: 1;
  font-size: 0.9rem;
}

.layer-opacity {
  width: 60px;
}

.layer-opacity input[type="range"] {
  width: 100%;
  height: 4px;
}

.time-slider {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 10px;
}

.time-btn {
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 50%;
  background: #3498db;
  color: white;
  font-size: 1.2rem;
  cursor: pointer;
  transition: all 0.2s;
}

.time-btn:hover {
  background: #2980b9;
  transform: scale(1.1);
}

.time-slider input[type="range"] {
  flex: 1;
  height: 6px;
}

.time-display {
  font-size: 0.9rem;
  color: #bdc3c7;
  min-width: 120px;
  text-align: right;
}

.time-speed {
  display: flex;
  align-items: center;
  gap: 10px;
}

.time-speed label {
  font-size: 0.9rem;
  color: #bdc3c7;
}

.time-speed select {
  padding: 4px 8px;
  border: 1px solid #34495e;
  border-radius: 4px;
  background: #1a1a1a;
  color: white;
}

.altitude-controls {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.altitude-range,
.slice-thickness {
  display: flex;
  align-items: center;
  gap: 8px;
}

.altitude-range label,
.slice-thickness label {
  font-size: 0.9rem;
  color: #bdc3c7;
  min-width: 80px;
}

.altitude-range input,
.slice-thickness input {
  width: 60px;
  padding: 4px;
  border: 1px solid #34495e;
  border-radius: 4px;
  background: #1a1a1a;
  color: white;
  text-align: center;
}

.legend-scale {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.legend-bar {
  height: 20px;
  border-radius: 4px;
  border: 1px solid #34495e;
}

.legend-labels {
  display: flex;
  justify-content: space-between;
  font-size: 0.8rem;
  color: #bdc3c7;
}

.legend-unit {
  text-align: center;
  font-size: 0.9rem;
  color: #3498db;
  font-weight: 600;
}

.tool-buttons {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
  margin-bottom: 15px;
}

.tool-btn {
  padding: 8px 12px;
  border: 1px solid #34495e;
  border-radius: 4px;
  background: #2c3e50;
  color: white;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 0.9rem;
}

.tool-btn:hover {
  background: #34495e;
}

.tool-btn.active {
  background: #3498db;
  border-color: #2980b9;
}

.measurement-results {
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-height: 150px;
  overflow-y: auto;
}

.result-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 8px;
  background: rgba(52, 73, 94, 0.3);
  border-radius: 4px;
  font-size: 0.9rem;
}

.remove-btn {
  width: 20px;
  height: 20px;
  border: none;
  border-radius: 50%;
  background: #e74c3c;
  color: white;
  cursor: pointer;
  font-size: 0.8rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.remove-btn:hover {
  background: #c0392b;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: rgba(44, 62, 80, 0.3);
}

::-webkit-scrollbar-thumb {
  background: #34495e;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #3498db;
}
</style>
