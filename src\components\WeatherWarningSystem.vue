<template>
  <div class="weather-system">
    <!-- 顶部导航栏 -->
    <header class="system-header">
      <div class="header-left">
        <h1>🌩️ 低空三维空域气象预警系统</h1>
        <div class="system-status">
          <span class="status-indicator" :class="systemStatus"></span>
          <span>{{ systemStatusText }}</span>
        </div>
      </div>
      <div class="header-right">
        <div class="current-time">{{ currentTime }}</div>
        <div class="weather-update">
          数据更新: {{ lastUpdateTime }}
        </div>
      </div>
    </header>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 左侧控制面板 -->
      <aside class="control-panel">
        <div class="panel-section">
          <h3>🎛️ 显示控制</h3>
          <div class="control-group">
            <label class="checkbox-label">
              <input type="checkbox" v-model="displayOptions.temperature" />
              <span>温度分布</span>
            </label>
            <label class="checkbox-label">
              <input type="checkbox" v-model="displayOptions.wind" />
              <span>风场信息</span>
            </label>
            <label class="checkbox-label">
              <input type="checkbox" v-model="displayOptions.precipitation" />
              <span>降水分布</span>
            </label>
            <label class="checkbox-label">
              <input type="checkbox" v-model="displayOptions.visibility" />
              <span>能见度</span>
            </label>
            <label class="checkbox-label">
              <input type="checkbox" v-model="displayOptions.turbulence" />
              <span>湍流区域</span>
            </label>
          </div>
        </div>

        <div class="panel-section">
          <h3>📏 高度控制</h3>
          <div class="altitude-control">
            <label>显示高度范围 (米)</label>
            <div class="range-inputs">
              <input type="number" v-model="altitudeRange.min" min="0" max="3000" placeholder="最低" />
              <span>-</span>
              <input type="number" v-model="altitudeRange.max" min="0" max="3000" placeholder="最高" />
            </div>
            <div class="altitude-slider">
              <input type="range" v-model="currentAltitude" min="0" max="3000" step="100" />
              <div class="slider-label">当前高度: {{ currentAltitude }}m</div>
            </div>
          </div>
        </div>

        <div class="panel-section">
          <h3>🛩️ 飞行路径</h3>
          <div class="flight-controls">
            <button @click="startFlightPlanning" class="btn btn-primary">
              规划路径
            </button>
            <button @click="clearFlightPath" class="btn btn-secondary">
              清除路径
            </button>
            <div class="flight-info" v-if="flightPath.length > 0">
              <p>路径点数: {{ flightPath.length }}</p>
              <p>预计飞行时间: {{ estimatedFlightTime }}分钟</p>
            </div>
          </div>
        </div>

        <div class="panel-section">
          <h3>⚠️ 预警设置</h3>
          <div class="warning-settings">
            <div class="warning-item">
              <label>风速预警 (m/s)</label>
              <input type="number" v-model="warningThresholds.windSpeed" min="0" max="50" />
            </div>
            <div class="warning-item">
              <label>能见度预警 (km)</label>
              <input type="number" v-model="warningThresholds.visibility" min="0" max="10" step="0.1" />
            </div>
            <div class="warning-item">
              <label>湍流强度预警</label>
              <select v-model="warningThresholds.turbulence">
                <option value="light">轻度</option>
                <option value="moderate">中度</option>
                <option value="severe">重度</option>
              </select>
            </div>
          </div>
        </div>
      </aside>

      <!-- 中央三维显示区域 -->
      <main class="visualization-area">
        <WeatherVisualization ref="weatherViz" />

        <!-- 模式切换标签 -->
        <div class="mode-tabs">
          <button
            @click="currentMode = 'weather'"
            class="mode-tab"
            :class="{ active: currentMode === 'weather' }"
          >
            🌤️ 气象监测
          </button>
          <button
            @click="currentMode = 'planning'"
            class="mode-tab"
            :class="{ active: currentMode === 'planning' }"
          >
            🛩️ 路径规划
          </button>
        </div>

        <!-- 飞行规划面板 -->
        <div v-if="currentMode === 'planning'" class="planning-panel">
          <FlightPlanner ref="flightPlanner" />
        </div>

        <!-- 加载状态 -->
        <div v-if="isLoading" class="loading-overlay">
          <div class="loading-spinner"></div>
          <p>正在加载气象数据...</p>
        </div>
      </main>

      <!-- 右侧信息面板 -->
      <aside class="info-panel">
        <div class="panel-section">
          <h3>🌡️ 实时气象</h3>
          <div class="weather-data">
            <div class="data-item">
              <span class="label">温度:</span>
              <span class="value">{{ currentWeather.temperature }}°C</span>
            </div>
            <div class="data-item">
              <span class="label">风速:</span>
              <span class="value">{{ currentWeather.windSpeed }} m/s</span>
            </div>
            <div class="data-item">
              <span class="label">风向:</span>
              <span class="value">{{ currentWeather.windDirection }}°</span>
            </div>
            <div class="data-item">
              <span class="label">能见度:</span>
              <span class="value">{{ currentWeather.visibility }} km</span>
            </div>
            <div class="data-item">
              <span class="label">湿度:</span>
              <span class="value">{{ currentWeather.humidity }}%</span>
            </div>
          </div>
        </div>

        <div class="panel-section">
          <h3>⚠️ 活跃预警</h3>
          <div class="warning-list">
            <div 
              v-for="warning in activeWarnings" 
              :key="warning.id"
              class="warning-item"
              :class="warning.level"
            >
              <div class="warning-header">
                <span class="warning-icon">{{ getWarningIcon(warning.type) }}</span>
                <span class="warning-title">{{ warning.title }}</span>
              </div>
              <div class="warning-content">
                <p>{{ warning.description }}</p>
                <div class="warning-meta">
                  <span>高度: {{ warning.altitude }}m</span>
                  <span>时间: {{ warning.time }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="panel-section">
          <h3>📊 数据统计</h3>
          <div class="statistics">
            <div class="stat-item">
              <span class="stat-label">监测站点</span>
              <span class="stat-value">{{ statistics.stations }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">数据更新频率</span>
              <span class="stat-value">{{ statistics.updateFreq }}分钟</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">预警区域</span>
              <span class="stat-value">{{ statistics.warningAreas }}</span>
            </div>
          </div>
        </div>
      </aside>
    </div>

    <!-- 底部状态栏 -->
    <footer class="status-bar">
      <div class="status-left">
        <span>坐标: {{ mouseCoordinates.lat.toFixed(4) }}°, {{ mouseCoordinates.lon.toFixed(4) }}°</span>
        <span>高度: {{ mouseCoordinates.alt }}m</span>
      </div>
      <div class="status-right">
        <span>系统版本: v2.1.0</span>
        <span>连接状态: {{ connectionStatus }}</span>
      </div>
    </footer>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed } from 'vue'
import WeatherVisualization from './WeatherVisualization.vue'
import FlightPlanner from './FlightPlanner.vue'
import weatherDataService from '../services/weatherDataService.js'

// 响应式数据
const systemStatus = ref('online')
const currentTime = ref('')
const lastUpdateTime = ref('')
const isLoading = ref(false)
const currentAltitude = ref(1000)
const flightPath = ref([])
const mouseCoordinates = ref({ lat: 0, lon: 0, alt: 0 })
const connectionStatus = ref('已连接')
const currentMode = ref('weather')

// 组件引用
const weatherViz = ref(null)
const flightPlanner = ref(null)

// 显示选项
const displayOptions = ref({
  temperature: true,
  wind: true,
  precipitation: false,
  visibility: true,
  turbulence: true
})

// 高度范围
const altitudeRange = ref({
  min: 0,
  max: 3000
})

// 预警阈值
const warningThresholds = ref({
  windSpeed: 15,
  visibility: 1.0,
  turbulence: 'moderate'
})

// 当前气象数据
const currentWeather = ref({
  temperature: 18.5,
  windSpeed: 8.2,
  windDirection: 245,
  visibility: 5.2,
  humidity: 65
})

// 活跃预警
const activeWarnings = ref([
  {
    id: 1,
    type: 'wind',
    level: 'warning',
    title: '强风预警',
    description: '预计未来2小时内风速将达到20m/s，建议暂停低空飞行作业',
    altitude: '500-1500',
    time: '14:30'
  },
  {
    id: 2,
    type: 'visibility',
    level: 'caution',
    title: '能见度不良',
    description: '局部区域能见度降至1km以下，影响飞行安全',
    altitude: '0-800',
    time: '14:25'
  }
])

// 统计数据
const statistics = ref({
  stations: 156,
  updateFreq: 5,
  warningAreas: 3
})

// 计算属性
const systemStatusText = computed(() => {
  const statusMap = {
    online: '系统正常',
    warning: '系统警告',
    offline: '系统离线'
  }
  return statusMap[systemStatus.value] || '未知状态'
})

const estimatedFlightTime = computed(() => {
  return Math.round(flightPath.value.length * 2.5)
})

// 生命周期
onMounted(() => {
  initializeSystem()
  startTimeUpdate()
  initializeCesium()
})

onUnmounted(() => {
  cleanup()
})

// 方法
const initializeSystem = () => {
  updateTime()
  lastUpdateTime.value = new Date().toLocaleTimeString()
}

const startTimeUpdate = () => {
  setInterval(updateTime, 1000)
}

const updateTime = () => {
  currentTime.value = new Date().toLocaleTimeString()
}

const initializeCesium = async () => {
  isLoading.value = true
  try {
    // 这里将初始化Cesium地球
    console.log('初始化Cesium地球...')
    // 模拟加载时间
    setTimeout(() => {
      isLoading.value = false
    }, 2000)
  } catch (error) {
    console.error('Cesium初始化失败:', error)
    isLoading.value = false
  }
}

const startFlightPlanning = () => {
  console.log('开始飞行路径规划')
  // 模拟添加路径点
  flightPath.value = [
    { lat: 39.9042, lon: 116.4074, alt: 500 },
    { lat: 39.9142, lon: 116.4174, alt: 800 },
    { lat: 39.9242, lon: 116.4274, alt: 1200 }
  ]
}

const clearFlightPath = () => {
  flightPath.value = []
}

const resetView = () => {
  console.log('重置视角')
}

const toggleFullscreen = () => {
  console.log('切换全屏')
}

const toggle3DMode = () => {
  console.log('切换2D/3D模式')
}

const getWarningIcon = (type) => {
  const iconMap = {
    wind: '💨',
    visibility: '🌫️',
    turbulence: '🌪️',
    precipitation: '🌧️',
    temperature: '🌡️'
  }
  return iconMap[type] || '⚠️'
}

const cleanup = () => {
  // 清理资源
}
</script>

<style scoped>
.weather-system {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: #1a1a1a;
  color: #ffffff;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.system-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 20px;
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  border-bottom: 2px solid #3498db;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.header-left h1 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
}

.system-status {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 5px;
  font-size: 0.9rem;
}

.status-indicator {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: #27ae60;
}

.status-indicator.warning {
  background: #f39c12;
}

.status-indicator.offline {
  background: #e74c3c;
}

.header-right {
  text-align: right;
}

.current-time {
  font-size: 1.2rem;
  font-weight: 600;
  color: #3498db;
}

.weather-update {
  font-size: 0.9rem;
  color: #bdc3c7;
  margin-top: 2px;
}

.main-content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.control-panel,
.info-panel {
  width: 300px;
  background: #2c3e50;
  border-right: 1px solid #34495e;
  overflow-y: auto;
  padding: 20px;
}

.info-panel {
  border-right: none;
  border-left: 1px solid #34495e;
}

.panel-section {
  margin-bottom: 25px;
  padding-bottom: 20px;
  border-bottom: 1px solid #34495e;
}

.panel-section:last-child {
  border-bottom: none;
}

.panel-section h3 {
  margin: 0 0 15px 0;
  color: #3498db;
  font-size: 1.1rem;
}

.control-group {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 5px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.checkbox-label:hover {
  background: rgba(52, 73, 94, 0.5);
}

.altitude-control {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.range-inputs {
  display: flex;
  align-items: center;
  gap: 10px;
}

.range-inputs input {
  flex: 1;
  padding: 5px;
  border: 1px solid #34495e;
  border-radius: 4px;
  background: #1a1a1a;
  color: #ffffff;
}

.altitude-slider {
  margin-top: 10px;
}

.altitude-slider input[type="range"] {
  width: 100%;
  margin: 10px 0;
}

.slider-label {
  text-align: center;
  font-size: 0.9rem;
  color: #bdc3c7;
}

.flight-controls {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s;
}

.btn-primary {
  background: #3498db;
  color: white;
}

.btn-primary:hover {
  background: #2980b9;
}

.btn-secondary {
  background: #95a5a6;
  color: white;
}

.btn-secondary:hover {
  background: #7f8c8d;
}

.flight-info {
  margin-top: 10px;
  padding: 10px;
  background: rgba(52, 73, 94, 0.3);
  border-radius: 4px;
  font-size: 0.9rem;
}

.warning-settings {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.warning-item {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.warning-item label {
  font-size: 0.9rem;
  color: #bdc3c7;
}

.warning-item input,
.warning-item select {
  padding: 5px;
  border: 1px solid #34495e;
  border-radius: 4px;
  background: #1a1a1a;
  color: #ffffff;
}

.visualization-area {
  flex: 1;
  position: relative;
  background: #000;
  display: flex;
  flex-direction: column;
}

.mode-tabs {
  position: absolute;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1000;
  display: flex;
  background: rgba(44, 62, 80, 0.95);
  border-radius: 8px;
  padding: 4px;
  backdrop-filter: blur(10px);
}

.mode-tab {
  padding: 8px 16px;
  border: none;
  background: transparent;
  color: #bdc3c7;
  cursor: pointer;
  border-radius: 6px;
  transition: all 0.2s;
  font-size: 0.9rem;
}

.mode-tab:hover {
  background: rgba(52, 152, 219, 0.3);
  color: white;
}

.mode-tab.active {
  background: #3498db;
  color: white;
}

.planning-panel {
  position: absolute;
  top: 0;
  right: 0;
  width: 400px;
  height: 100%;
  background: rgba(44, 62, 80, 0.95);
  backdrop-filter: blur(10px);
  z-index: 999;
  overflow: hidden;
}

.cesium-container {
  width: 100%;
  height: 100%;
}

.view-controls {
  position: absolute;
  top: 20px;
  right: 20px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  z-index: 1000;
}

.control-btn {
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 50%;
  background: rgba(44, 62, 80, 0.9);
  color: white;
  font-size: 1.2rem;
  cursor: pointer;
  transition: all 0.2s;
  backdrop-filter: blur(10px);
}

.control-btn:hover {
  background: rgba(52, 152, 219, 0.9);
  transform: scale(1.1);
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 2000;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 4px solid #34495e;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.weather-data {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.data-item {
  display: flex;
  justify-content: space-between;
  padding: 8px;
  background: rgba(52, 73, 94, 0.3);
  border-radius: 4px;
}

.label {
  color: #bdc3c7;
}

.value {
  color: #3498db;
  font-weight: 600;
}

.warning-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
  max-height: 300px;
  overflow-y: auto;
}

.warning-item {
  padding: 12px;
  border-radius: 6px;
  border-left: 4px solid;
}

.warning-item.warning {
  background: rgba(231, 76, 60, 0.1);
  border-left-color: #e74c3c;
}

.warning-item.caution {
  background: rgba(243, 156, 18, 0.1);
  border-left-color: #f39c12;
}

.warning-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.warning-title {
  font-weight: 600;
  color: #ffffff;
}

.warning-content p {
  margin: 0 0 8px 0;
  font-size: 0.9rem;
  color: #bdc3c7;
}

.warning-meta {
  display: flex;
  gap: 15px;
  font-size: 0.8rem;
  color: #95a5a6;
}

.statistics {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  padding: 8px;
  background: rgba(52, 73, 94, 0.3);
  border-radius: 4px;
}

.stat-label {
  color: #bdc3c7;
}

.stat-value {
  color: #27ae60;
  font-weight: 600;
}

.status-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 20px;
  background: #34495e;
  border-top: 1px solid #2c3e50;
  font-size: 0.9rem;
  color: #bdc3c7;
}

.status-left,
.status-right {
  display: flex;
  gap: 20px;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #2c3e50;
}

::-webkit-scrollbar-thumb {
  background: #34495e;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #3498db;
}
</style>
