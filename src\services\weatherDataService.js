/**
 * 气象数据服务
 * 模拟实时气象数据获取和处理
 */

class WeatherDataService {
  constructor() {
    this.isConnected = false
    this.updateInterval = null
    this.subscribers = new Map()
    this.currentData = {
      timestamp: new Date(),
      stations: [],
      gridData: {},
      warnings: [],
      forecasts: []
    }
    
    this.initializeData()
  }

  /**
   * 初始化基础数据
   */
  initializeData() {
    // 生成模拟气象站点
    this.generateStations()
    
    // 生成初始网格数据
    this.generateGridData()
    
    // 生成初始预警信息
    this.generateWarnings()
  }

  /**
   * 生成模拟气象站点
   */
  generateStations() {
    const stations = []
    const centerLat = 39.9042
    const centerLon = 116.4074
    const range = 2.0 // 经纬度范围

    for (let i = 0; i < 50; i++) {
      const station = {
        id: `STATION_${i.toString().padStart(3, '0')}`,
        name: `气象站${i + 1}`,
        latitude: centerLat + (Math.random() - 0.5) * range,
        longitude: centerLon + (Math.random() - 0.5) * range,
        altitude: Math.random() * 200 + 50,
        type: Math.random() > 0.7 ? 'automatic' : 'manual',
        status: Math.random() > 0.1 ? 'online' : 'offline',
        data: this.generateStationData()
      }
      stations.push(station)
    }

    this.currentData.stations = stations
  }

  /**
   * 生成单个站点的气象数据
   */
  generateStationData() {
    return {
      temperature: Math.round((Math.random() * 30 - 5) * 10) / 10,
      humidity: Math.round(Math.random() * 100),
      pressure: Math.round((1000 + Math.random() * 50) * 10) / 10,
      windSpeed: Math.round(Math.random() * 25 * 10) / 10,
      windDirection: Math.round(Math.random() * 360),
      visibility: Math.round(Math.random() * 10 * 10) / 10,
      precipitation: Math.round(Math.random() * 5 * 10) / 10,
      cloudCover: Math.round(Math.random() * 100),
      uvIndex: Math.round(Math.random() * 11),
      timestamp: new Date()
    }
  }

  /**
   * 生成网格化气象数据
   */
  generateGridData() {
    const gridData = {
      temperature: this.generateGridField('temperature', -5, 35),
      windSpeed: this.generateGridField('windSpeed', 0, 25),
      windDirection: this.generateGridField('windDirection', 0, 360),
      precipitation: this.generateGridField('precipitation', 0, 10),
      visibility: this.generateGridField('visibility', 0.1, 10),
      turbulence: this.generateGridField('turbulence', 0, 1),
      cloudCover: this.generateGridField('cloudCover', 0, 100)
    }

    this.currentData.gridData = gridData
  }

  /**
   * 生成单个气象要素的网格数据
   */
  generateGridField(fieldName, minValue, maxValue) {
    const grid = {
      bounds: {
        north: 40.5,
        south: 39.3,
        east: 117.0,
        west: 115.8
      },
      resolution: 0.05, // 网格分辨率
      data: []
    }

    const latSteps = Math.ceil((grid.bounds.north - grid.bounds.south) / grid.resolution)
    const lonSteps = Math.ceil((grid.bounds.east - grid.bounds.west) / grid.resolution)

    for (let i = 0; i < latSteps; i++) {
      const row = []
      for (let j = 0; j < lonSteps; j++) {
        const lat = grid.bounds.south + i * grid.resolution
        const lon = grid.bounds.west + j * grid.resolution
        
        // 添加一些空间相关性
        const noise = Math.sin(lat * 10) * Math.cos(lon * 10) * 0.3
        const baseValue = minValue + Math.random() * (maxValue - minValue)
        const value = Math.max(minValue, Math.min(maxValue, baseValue + noise))
        
        row.push({
          latitude: lat,
          longitude: lon,
          value: Math.round(value * 100) / 100,
          altitude: Math.random() * 3000 // 0-3000米高度
        })
      }
      grid.data.push(row)
    }

    return grid
  }

  /**
   * 生成预警信息
   */
  generateWarnings() {
    const warnings = []
    const warningTypes = [
      { type: 'wind', name: '大风预警', icon: '💨' },
      { type: 'visibility', name: '低能见度预警', icon: '🌫️' },
      { type: 'turbulence', name: '湍流预警', icon: '🌪️' },
      { type: 'precipitation', name: '降水预警', icon: '🌧️' },
      { type: 'temperature', name: '极端温度预警', icon: '🌡️' }
    ]

    // 随机生成一些预警
    for (let i = 0; i < Math.random() * 5; i++) {
      const warningType = warningTypes[Math.floor(Math.random() * warningTypes.length)]
      const levels = ['info', 'caution', 'warning', 'danger']
      const level = levels[Math.floor(Math.random() * levels.length)]
      
      const warning = {
        id: `WARNING_${Date.now()}_${i}`,
        type: warningType.type,
        name: warningType.name,
        icon: warningType.icon,
        level: level,
        title: `${warningType.name} - ${level.toUpperCase()}`,
        description: this.generateWarningDescription(warningType.type, level),
        area: {
          center: {
            latitude: 39.9042 + (Math.random() - 0.5) * 1.0,
            longitude: 116.4074 + (Math.random() - 0.5) * 1.0
          },
          radius: Math.random() * 50 + 10 // 10-60km半径
        },
        altitude: {
          min: Math.random() * 1000,
          max: Math.random() * 2000 + 1000
        },
        validFrom: new Date(),
        validTo: new Date(Date.now() + Math.random() * 6 * 60 * 60 * 1000), // 6小时内
        issuedBy: 'CAAC气象中心',
        issuedAt: new Date()
      }
      
      warnings.push(warning)
    }

    this.currentData.warnings = warnings
  }

  /**
   * 生成预警描述
   */
  generateWarningDescription(type, level) {
    const descriptions = {
      wind: {
        info: '预计风速将达到10-15m/s，请注意飞行安全',
        caution: '预计风速将达到15-20m/s，建议谨慎飞行',
        warning: '预计风速将达到20-25m/s，建议暂停低空飞行',
        danger: '预计风速将超过25m/s，严禁低空飞行作业'
      },
      visibility: {
        info: '能见度将降至3-5km，请注意观察',
        caution: '能见度将降至1-3km，建议减速飞行',
        warning: '能见度将降至0.5-1km，建议暂停飞行',
        danger: '能见度将低于0.5km，严禁飞行作业'
      },
      turbulence: {
        info: '预计出现轻度湍流，请系好安全带',
        caution: '预计出现中度湍流，建议调整飞行高度',
        warning: '预计出现强湍流，建议暂停飞行',
        danger: '预计出现极强湍流，严禁飞行作业'
      },
      precipitation: {
        info: '预计小雨，对飞行影响较小',
        caution: '预计中雨，请注意飞行安全',
        warning: '预计大雨，建议暂停飞行',
        danger: '预计暴雨，严禁飞行作业'
      },
      temperature: {
        info: '温度异常，请注意设备性能',
        caution: '极端温度，可能影响设备运行',
        warning: '严重温度异常，建议暂停作业',
        danger: '极端温度条件，严禁飞行作业'
      }
    }

    return descriptions[type]?.[level] || '请注意气象条件变化'
  }

  /**
   * 启动实时数据更新
   */
  startRealTimeUpdates(intervalMs = 30000) {
    if (this.updateInterval) {
      clearInterval(this.updateInterval)
    }

    this.isConnected = true
    this.updateInterval = setInterval(() => {
      this.updateData()
      this.notifySubscribers('dataUpdate', this.currentData)
    }, intervalMs)

    console.log('气象数据实时更新已启动')
  }

  /**
   * 停止实时数据更新
   */
  stopRealTimeUpdates() {
    if (this.updateInterval) {
      clearInterval(this.updateInterval)
      this.updateInterval = null
    }
    this.isConnected = false
    console.log('气象数据实时更新已停止')
  }

  /**
   * 更新数据
   */
  updateData() {
    // 更新站点数据
    this.currentData.stations.forEach(station => {
      if (station.status === 'online') {
        station.data = this.generateStationData()
      }
    })

    // 更新网格数据
    this.generateGridData()

    // 偶尔更新预警信息
    if (Math.random() < 0.1) {
      this.generateWarnings()
    }

    this.currentData.timestamp = new Date()
  }

  /**
   * 订阅数据更新
   */
  subscribe(eventType, callback) {
    if (!this.subscribers.has(eventType)) {
      this.subscribers.set(eventType, new Set())
    }
    this.subscribers.get(eventType).add(callback)
  }

  /**
   * 取消订阅
   */
  unsubscribe(eventType, callback) {
    if (this.subscribers.has(eventType)) {
      this.subscribers.get(eventType).delete(callback)
    }
  }

  /**
   * 通知订阅者
   */
  notifySubscribers(eventType, data) {
    if (this.subscribers.has(eventType)) {
      this.subscribers.get(eventType).forEach(callback => {
        try {
          callback(data)
        } catch (error) {
          console.error('订阅者回调执行失败:', error)
        }
      })
    }
  }

  /**
   * 获取当前数据
   */
  getCurrentData() {
    return { ...this.currentData }
  }

  /**
   * 获取指定区域的数据
   */
  getDataForArea(bounds) {
    const filteredStations = this.currentData.stations.filter(station => 
      station.latitude >= bounds.south &&
      station.latitude <= bounds.north &&
      station.longitude >= bounds.west &&
      station.longitude <= bounds.east
    )

    return {
      stations: filteredStations,
      gridData: this.currentData.gridData,
      warnings: this.currentData.warnings.filter(warning =>
        this.isPointInBounds(warning.area.center, bounds)
      )
    }
  }

  /**
   * 检查点是否在边界内
   */
  isPointInBounds(point, bounds) {
    return point.latitude >= bounds.south &&
           point.latitude <= bounds.north &&
           point.longitude >= bounds.west &&
           point.longitude <= bounds.east
  }

  /**
   * 获取连接状态
   */
  getConnectionStatus() {
    return {
      isConnected: this.isConnected,
      lastUpdate: this.currentData.timestamp,
      stationsOnline: this.currentData.stations.filter(s => s.status === 'online').length,
      totalStations: this.currentData.stations.length
    }
  }
}

// 创建单例实例
const weatherDataService = new WeatherDataService()

export default weatherDataService
