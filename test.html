<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cesium 地球测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #f0f0f0;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .info {
            background: #e8f5e8;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border-left: 4px solid #4CAF50;
        }
        .link {
            display: inline-block;
            background: #007bff;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 5px;
            margin: 10px 0;
        }
        .link:hover {
            background: #0056b3;
        }
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .feature {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            border: 1px solid #dee2e6;
        }
        .feature h3 {
            margin-top: 0;
            color: #495057;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌍 Cesium 地球应用</h1>
        
        <div class="info">
            <strong>应用状态：</strong> 多版本地球应用已成功创建！
        </div>

        <p>这是一个包含多种地球实现方式的Vue 3应用，你可以选择不同的地球体验。</p>
        
        <a href="http://localhost:5173/" class="link" target="_blank">🚀 打开地球应用</a>
        
        <div class="features">
            <div class="feature">
                <h3>🎨 简单地球</h3>
                <p>基于CSS 3D的轻量级地球实现，无需外部依赖，支持基本交互。</p>
            </div>

            <div class="feature">
                <h3>🌐 Cesium CDN</h3>
                <p>使用Cesium CDN的专业级3D地球，支持真实地理数据和高质量渲染。</p>
            </div>

            <div class="feature">
                <h3>⚡ Cesium 本地</h3>
                <p>本地部署的Cesium地球，可高度定制，性能优秀。</p>
            </div>

            <div class="feature">
                <h3>🎮 多种交互</h3>
                <p>支持鼠标拖拽、滚轮缩放、快速导航等多种交互方式。</p>
            </div>
        </div>
        
        <h2>使用说明</h2>
        <ul>
            <li><strong>鼠标左键拖拽：</strong>旋转地球</li>
            <li><strong>鼠标右键拖拽：</strong>平移视图</li>
            <li><strong>鼠标滚轮：</strong>缩放地球</li>
            <li><strong>控制按钮：</strong>快速飞行到指定位置</li>
            <li><strong>主页按钮：</strong>重置到默认视角</li>
        </ul>
        
        <h2>技术栈</h2>
        <ul>
            <li><strong>Vue 3：</strong>渐进式JavaScript框架</li>
            <li><strong>Vite：</strong>下一代前端构建工具</li>
            <li><strong>Cesium：</strong>开源3D地球和地图库</li>
        </ul>
    </div>
</body>
</html>
